package keto

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"strings"
)

// group policy template
const (
	GroupNs         = "IamGroup"
	GroupRelMembers = "members"

	// # group members policy
	// #IamPolicy:IamGroup.<group-name>.member#roles@IamRole:member
	// #IamPolicy:IamGroup.<group-name>.member#users@IamGroup:<group-name>.member#members
	// #IamGroup:<group-name>#policies@IamPolicy:IamGroup.<group-name>.member

	groupTpl = `
# group owners policy
IamPolicy:IamGroup.<group-name>.owner#roles@IamRole:owner
IamPolicy:IamGroup.<group-name>.owner#users@IamGroup:<group-name>.owner#members
IamGroup:<group-name>#policies@IamPolicy:IamGroup.<group-name>.owner
IamGroup:<group-name>#policies@IamPolicy:sys/admin

# group managers policy
IamPolicy:IamGroup.<group-name>.manager#roles@IamRole:manager
IamPolicy:IamGroup.<group-name>.manager#users@IamGroup:<group-name>.manager#members
IamGroup:<group-name>#policies@IamPolicy:IamGroup.<group-name>.manager

# make team managers can create new things
AnnoLot:IamGroup.<group-name>#parents@IamGroup:<group-name>
AnnoOrder:IamGroup.<group-name>#parents@IamGroup:<group-name>
AnnofeedData:IamGroup.<group-name>#parents@IamGroup:<group-name>
AnnofeedFile:IamGroup.<group-name>#parents@IamGroup:<group-name>
`
	// TODO: 当新加资源类型时，如何使用户可以在已有的 Group 以及新增的 Group 里进行创建/查询
	//       Option 1. 每当用户新建资源时，同时检查用户所在的 Org 以及资源的 parent group 是否存在规则
	//                 xxx:IamGroup.<group-name>#parents@IamGroup:<group-name>
	//                 如不存在，则增加；可使用缓存以减少查询；是否需要对用户所在的所有 Group 进行;
	//                 但如何解决在新建资源之前，查询资源列表的权限问题？

	groupOwnerTpl = `
IamGroup:<group-name>.owner#members@IamUser:<owner-user>
IamGroup:<group-name>#members@IamUser:<owner-user>
IamGroup:<group-name>#members@IamUser:<owner-user>
IamUser:<owner-user>#parents@IamGroup:<group-name>
`

	groupParentTpl = `IamGroup:<group-name>#parents@IamGroup:<parent-group>` + "\n"

	orgTpl = `
# make top-team managers can create new things
IamRole:IamGroup.<group-name>#parents@IamGroup:<group-name>
IamUser:IamGroup.<group-name>#parents@IamGroup:<group-name>
IamGroup:IamGroup.<group-name>#parents@IamGroup:<group-name>
`
)

func init() {
	// validate templates
	for _, s := range []string{groupTpl, groupOwnerTpl, groupParentTpl, orgTpl} {
		_, err := ParseTuples(s)
		if err != nil {
			panic(fmt.Errorf("failed to parse group policy template\n: %v\n: %w", s, err))
		}
	}
}

func MakeGroupName(uid string) string { return MakeObjectName(GroupNs, uid) }

func (o *AccessMgr) CreateGroup(ctx context.Context, uid, owner, parent string) (err error) {
	s := groupTpl
	if owner != "" {
		s += strings.ReplaceAll(groupOwnerTpl, "<owner-user>", owner)
	}
	if parent == "" {
		s += orgTpl
	} else {
		s += strings.ReplaceAll(groupParentTpl, "<parent-group>", parent)
	}
	s = strings.ReplaceAll(s, "<group-name>", uid)
	tps, err := ParseTuples(s)
	if err != nil {
		return fmt.Errorf("failed to parse group policy template: %q: %w", s, err)
	}
	return o.kt.Add(ctx, tps...)
}

func (o *AccessMgr) DeleteGroup(ctx context.Context, uid string) (err error) {
	// TODO: delete IamGroup:<uid>.<role> tuples
	return o.DeleteObject(ctx, MakeObjectName(GroupNs, uid))
}

func (o *AccessMgr) AddGroupMembers(ctx context.Context, uid, role string, setParent bool, members []string) (err error) {
	fmt.Printf("---> Add group members.\n")
	fmt.Printf("---> uid: %s, role: %s, setParent: %t, members: %v\n", uid, role, setParent, members)
	tps, err := o.makeGroupMemberTuple(uid, role, setParent, members)
	spew.Dump("---> tps: ", tps)
	if err != nil {
		return err
	}
	return o.kt.Add(ctx, tps...)
}

func (o *AccessMgr) DeleteGroupMembers(ctx context.Context, uid, role string, setParent bool, members []string) (err error) {
	tps, err := o.makeGroupMemberTuple(uid, role, setParent, members)
	if err != nil {
		return err
	}
	return o.kt.Delete(ctx, tps...)
}

func (o *AccessMgr) makeGroupMemberTuple(uid, role string, setParent bool, members []string) (tps []*RelationTuple, err error) {
	obj := uid
	if role != "member" {
		// create group objects derived from group uid and role
		obj += "." + role
	}
	for _, e := range members {
		ns, id, err := ParseUserGroupName(e)
		if err != nil {
			return nil, err
		}
		relation := ""
		if ns == GroupNs {
			relation = GroupRelMembers
		}
		tps = append(tps, NewTuple(GroupNs, uid, GroupRelMembers, NewSubjectSet(ns, id, relation)))
		if obj != uid {
			tps = append(tps, NewTuple(GroupNs, obj, GroupRelMembers, NewSubjectSet(ns, id, relation)))
		}
		if setParent {
			tps = append(tps, NewTuple(ns, id, RelParents, NewSubjectSet(GroupNs, obj, "")))
		}
	}
	return
}

func (o *AccessMgr) AddSysGroupMembers(ctx context.Context, role string, members ...string) (err error) {
	tps, err := o.makeSysGroupTuples(role, members)
	spew.Dump("---> add keto tps: ", tps)
	if err != nil {
		return err
	}
	return o.kt.Add(ctx, tps...)
}

func (o *AccessMgr) DeleteSysGroupMembers(ctx context.Context, role string, members ...string) (err error) {
	tps, err := o.makeSysGroupTuples(role, members)
	if err != nil {
		return err
	}
	return o.kt.Delete(ctx, tps...)
}

func (o *AccessMgr) makeSysGroupTuples(role string, members []string) (tps []*RelationTuple, err error) {
	for _, e := range members {
		ns, id, err := ParseUserGroupName(e)
		if err != nil {
			return nil, err
		}
		if ns != UserNs {
			return nil, fmt.Errorf("bad sys role member: %v", e)
		}
		tps = append(tps, NewTuple(GroupNs, SysPrefix+role, GroupRelMembers, NewSubjectSet(ns, id, "")))
	}
	return
}
