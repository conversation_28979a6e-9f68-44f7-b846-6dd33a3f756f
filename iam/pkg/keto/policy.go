package keto

import (
	"context"
	"fmt"
	"strings"

	acl "github.com/ory/keto/proto/ory/keto/relation_tuples/v1alpha2"
	keto "github.com/ory/keto/proto/ory/keto/relation_tuples/v1alpha2"
)

const (
	PolicyNs       = "IamPolicy"
	PolicyRelRoles = "roles"
	PolicyRelUsers = "users"
)

type Policy struct {
	Name  string
	Role  string
	Users []string
}

func MakePolicyName(name string) string { return MakeObjectName(PolicyNs, name) }

// GetPolicy returns the roles and users (groups) mentioned in the policy.
// If the number of roles or users exceeds 100, only 100 items are returned.
func (o *AccessMgr) GetPolicy(ctx context.Context, name string) (policy *Policy, err error) {
	rsp, err := o.kt.List(ctx, &acl.ListRelationTuplesRequest{
		RelationQuery: NewQuery(PolicyNs, name, PolicyRelRoles, nil),
	})
	if err != nil {
		return
	}
	if n := len(rsp.RelationTuples); n == 0 {
		return nil, nil
	} else if n != 1 {
		o.kt.log.Warn(ctx, "policy binds with multile roles", "policy", name, "count", n)
	}
	subrole := rsp.RelationTuples[0].Subject
	set := subrole.GetSet()
	if set == nil || set.Namespace != RoleNs {
		return nil, fmt.Errorf("expect a role, got %v", FormatSubject(subrole))
	}

	rsp, err = o.kt.List(ctx, &acl.ListRelationTuplesRequest{
		RelationQuery: NewQuery(PolicyNs, name, PolicyRelUsers, nil),
	})
	if err != nil {
		return
	}
	policy = &Policy{Name: name, Role: set.Object}
	for _, t := range rsp.RelationTuples {
		sub := t.Subject.GetSet()
		policy.Users = append(policy.Users, MakeObjectName(sub.Namespace, sub.Object))
	}
	return
}

func (o *AccessMgr) GetResourcePolicyByRole(ctx context.Context, resource, role string) (policy *Policy, err error) {
	name := EscapeName(resource) + "." + role
	return o.GetPolicy(ctx, name)
}

// CreatePolicy creates a new policy, attaches it to the resource and returns the policy's name.
func (o *AccessMgr) CreatePolicy(ctx context.Context, resource, role string, users []string) (name string, err error) {
	rns, robj := ParseObjectName(resource)
	if rns == "" {
		return "", fmt.Errorf("invalid resource name %q", resource)
	}

	robj = EscapeName(robj)
	name = EscapeName(resource) + "." + role
	tps, err := o.makePolicyUsersTuples(name, users)
	if err != nil {
		return "", err
	}
	tps = append(tps, NewTuple(PolicyNs, name, PolicyRelRoles, NewSubjectSet(RoleNs, role, "")))
	tps = append(tps, NewTuple(rns, robj, RelPolicies, NewSubjectSet(PolicyNs, name, "")))
	fmt.Println("---> tps: ", tps)
	return name, o.kt.Add(ctx, tps...)
}

// EditPolicy updates the policy's binding users.
// TODO: calculate the delta changes and make the change in a transaction
func (o *AccessMgr) EditPolicy(ctx context.Context, name string, users []string) (err error) {
	// name = EscapeName(name)
	tps, err := o.makePolicyUsersTuples(name, users)
	if err != nil {
		return
	}
	fmt.Println("---> tps: ", tps)

	// delete previous relations
	q := NewQuery(PolicyNs, name, PolicyRelUsers, nil)
	if err := o.kt.DeleteByQuery(ctx, q); err != nil {
		return err
	}

	return o.kt.Add(ctx, tps...)
}

func (o *AccessMgr) RevokeUsersRoleOnResource(ctx context.Context, resource, role string, users []string) (err error) {
	name := EscapeName(resource) + "." + role
	return o.DeletePolicyUsers(ctx, name, users)
}

func (o *AccessMgr) DeletePolicyUsers(ctx context.Context, name string, users []string) (err error) {
	tps, err := o.makePolicyUsersTuples(name, users)
	if err != nil {
		return
	}
	return o.kt.Delete(ctx, tps...)
}

func (o *AccessMgr) makePolicyUsersTuples(name string, users []string) (tps []*RelationTuple, err error) {
	tps = make([]*RelationTuple, len(users))
	for i, e := range users {
		ns, obj, err := ParseUserGroupName(e)
		if err != nil {
			return nil, err
		}
		var sub *keto.Subject
		if ns == GroupNs {
			sub = NewSubjectSet(ns, obj, GroupRelMembers)
		} else {
			sub = NewSubjectSet(ns, obj, "")
		}
		tps[i] = NewTuple(PolicyNs, name, PolicyRelUsers, sub)
	}
	return
}

func (o *AccessMgr) DeletePolicy(ctx context.Context, name string) (err error) {
	// name = EscapeName(name)
	return o.kt.DeleteObj(ctx, PolicyNs, name)
}

func (o *AccessMgr) AttachPolicies(ctx context.Context, isDetach bool, policies []string, resources []string) (err error) {
	tps := []*RelationTuple{}
	for _, r := range resources {
		ns, obj := ParseObjectName(r)
		if ns == "" {
			return fmt.Errorf("invalid resource name %q", r)
		}
		for _, p := range policies {
			// p = EscapeName(p)
			tps = append(tps, NewTuple(ns, obj, RelPolicies, NewSubjectSet(PolicyNs, p, "")))
		}
	}

	if isDetach {
		return o.kt.Delete(ctx, tps...)
	}
	return o.kt.Add(ctx, tps...)
}

func (o *AccessMgr) GetAttachedPolicies(ctx context.Context, resource string) (policies []string, err error) {
	ns, obj := ParseObjectName(resource)
	if ns == "" {
		return nil, fmt.Errorf("invalid resource name %q", resource)
	}
	obj = EscapeName(obj)
	rsp, err := o.kt.List(ctx, &acl.ListRelationTuplesRequest{
		RelationQuery: NewQuery(ns, obj, RelPolicies, nil),
	})
	if err != nil {
		return
	}
	for _, t := range rsp.RelationTuples {
		set := t.Subject.GetSet()
		if set == nil {
			continue
		}
		// do not include system policies
		if !strings.HasPrefix(set.Object, SysPrefix) {
			policies = append(policies, set.Object)
		}
	}
	return
}

func (o *AccessMgr) GetPolicyAttachedResources(ctx context.Context, policy string) (resources []string, err error) {
	// policy = EscapeName(policy)
	rsp, err := o.kt.List(ctx, &acl.ListRelationTuplesRequest{
		RelationQuery: NewQuery("", "", RelPolicies, NewSubjectSet(PolicyNs, policy, "")),
	})
	if err != nil {
		return
	}
	for _, t := range rsp.RelationTuples {
		resources = append(resources, MakeObjectName(t.Namespace, t.Object))
	}
	return
}
