# 项目层级权限改造方案

## 1. 现状分析

### 1.1 当前架构
```
原有层级: Lot -> Job
权限继承: Job继承Lot权限，Lot直接关联组织
```

### 1.2 目标架构
```
新层级: Project -> Lot -> Job
权限继承: Job继承Lot权限，Lot继承Project权限，Project关联组织
```

### 1.3 历史数据问题
- 历史Lot数据：project_id = 0 或 NULL
- 历史权限规则：没有Project相关的权限元组
- 历史权限检查：直接基于Lot和组织

## 2. 兼容性方案分析

### 2.1 方案一：双轨制兼容（推荐）

#### 核心思路
- 历史数据保持原有权限检查逻辑
- 新数据使用新的三层权限检查逻辑
- 通过project_id是否为0来区分

#### 优势
- 最小化改动，风险最低
- 历史数据无需迁移
- 新老逻辑并存，平滑过渡

#### 劣势
- 代码逻辑稍复杂，需要双重判断
- 长期维护两套逻辑

### 2.2 方案二：统一迁移方案

#### 核心思路
- 创建默认项目（如project_id=0）
- 所有历史数据关联到默认项目
- 为默认项目创建权限规则
- 统一使用三层权限检查

#### 优势
- 逻辑统一，代码简洁
- 长期维护成本低

#### 劣势
- 需要数据迁移，风险较高
- 需要为历史数据创建大量权限规则
- 可能影响现有权限分配

### 2.3 推荐方案：双轨制兼容

**理由**：
1. 风险最低，不影响现有功能
2. 改动最小，开发成本低
3. 可以逐步迁移，给用户选择权
4. 符合"尽可能小的修改和兼容"的要求

## 3. 权限规则设计

### 3.1 静态权限规则（tuples.txt）

#### 3.1.1 AnnoProject角色权限定义
```
# AnnoProject基础权限
IamRole:AnnoProject.viewer#<EMAIL>
IamRole:AnnoProject.viewer#<EMAIL>
IamRole:AnnoProject.viewer#<EMAIL>
IamRole:AnnoProject.viewer#<EMAIL>
IamRole:AnnoProject.viewer#policies@IamPolicy:sys/admin
IamRole:AnnoProject.viewer#policies@IamPolicy:sys/publicView

IamRole:AnnoProject.editor#perms@IamRole:AnnoProject.viewer#perms
IamRole:AnnoProject.editor#<EMAIL>
IamRole:AnnoProject.editor#<EMAIL>
IamRole:AnnoProject.editor#<EMAIL>
IamRole:AnnoProject.editor#<EMAIL>
IamRole:AnnoProject.editor#policies@IamPolicy:sys/admin
IamRole:AnnoProject.editor#policies@IamPolicy:sys/publicView

IamRole:AnnoProject.owner#perms@IamRole:AnnoProject.editor#perms
IamRole:AnnoProject.owner#<EMAIL>
IamRole:AnnoProject.owner#<EMAIL>
IamRole:AnnoProject.owner#<EMAIL>
IamRole:AnnoProject.owner#policies@IamPolicy:sys/admin
IamRole:AnnoProject.owner#policies@IamPolicy:sys/publicView
```

#### 3.1.2 项目权限继承到Lot
```
# 项目权限自动继承到Lot
IamRole:AnnoProject.owner#perms@IamRole:AnnoLot.owner#perms
IamRole:AnnoProject.editor#perms@IamRole:AnnoLot.editor#perms
IamRole:AnnoProject.viewer#perms@IamRole:AnnoLot.viewer#perms
```

#### 3.1.3 复合角色集成Project权限
```
# anno业务角色集成Project权限
IamRole:anno.owner#perms@IamRole:AnnoProject.owner#perms
IamRole:anno.editor#perms@IamRole:AnnoProject.editor#perms
IamRole:anno.viewer#perms@IamRole:AnnoProject.viewer#perms

# KAM和PM角色集成Project权限
IamRole:anno.kam#perms@IamRole:AnnoProject.owner#perms
IamRole:anno.pm#perms@IamRole:AnnoProject.editor#perms
```

#### 3.1.4 全局策略应用
```
# 所有AnnoProject受全局策略控制
AnnoProject:*#policies@IamPolicy:sys/admin
AnnoProject:*#policies@IamPolicy:sys/inspector
AnnoProject:*#policies@IamPolicy:sys/kam
AnnoProject:*#policies@IamPolicy:sys/pm
```

### 3.2 动态权限规则

#### 3.2.1 Project权限模板（新增）
在`iam/pkg/keto/`中新增`project.go`文件：

```go
const (
    ProjectNs = "AnnoProject"
    
    projectTpl = `
# project owners policy
IamPolicy:AnnoProject.<project-name>.owner#roles@IamRole:AnnoProject.owner
AnnoProject:<project-name>#policies@IamPolicy:AnnoProject.<project-name>.owner
AnnoProject:<project-name>#policies@IamPolicy:sys/admin
`
    projectOwnerUserTpl  = "IamPolicy:AnnoProject.<project-name>.owner#users@IamUser:<subject-name>\n"
    projectOwnerGroupTpl = "IamPolicy:AnnoProject.<project-name>.owner#users@IamGroup:<subject-name>#members\n"
    projectParentTpl     = "AnnoProject:<project-name>#parents@<parent>\n"
)
```

#### 3.2.2 Lot权限模板修改
修改`iam/pkg/keto/object.go`中的资源模板，支持Project父级：

```go
// 在resourceParentTpl中支持AnnoProject父级
const resourceParentTpl = "<namespace>:<resource-name>#parents@<parent>\n"
```

### 3.3 命名空间定义（namespaces.keto.ts）

#### 3.3.1 AnnoProject命名空间
```typescript
class AnnoProject implements Namespace {
  related: {
    parents: IamGroup[]  // 项目所属的组织
    policies: IamPolicy[]
    lots: AnnoLot[]  // 项目下的批次
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoProject:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.setPolicy"),

    // 项目特有权限
    createLot: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.createLot"),
    listLot: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.listLot"),
    manageMember: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.manageMember"),
    stat: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.stat"),
  }
}
```

#### 3.3.2 AnnoLot命名空间修改
```typescript
class AnnoLot implements Namespace {
  related: {
    parents: (IamGroup|AnnoLot|AnnoProject)[]  // 添加AnnoProject作为父级
    policies: IamPolicy[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoLot:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),
    // ... 其他权限方法保持不变
  }
}
```

## 4. 业务代码改造

### 4.1 权限常量定义

#### 4.1.1 新增Project权限常量
在`anno/internal/biz/perms.go`中添加：

```go
const (
    // 现有权限保持不变...
    
    // Project权限
    PermCreateLot     = "createLot"
    PermListLot       = "listLot"
    PermManageMember  = "manageMember"
)

const (
    // 现有权限类保持不变...
    
    // Project权限类
    PermClsProject = "AnnoProject"
)

// Project作用域函数
func ProjectScope(uid string) string { return ResourceScope(PermClsProject, uid) }
```

### 4.2 Lot创建权限检查改造

#### 4.2.1 双轨制权限检查逻辑
在`anno/internal/service/lot.go`中修改CreateLot方法：

```go
func (o *LotsService) CreateLot(ctx context.Context, req *anno.CreateLotRequest) (*anno.Lot, error) {
    // ... 现有逻辑保持不变 ...
    
    // 双轨制权限检查
    var hasPermission bool
    
    if req.ProjectUid != "" && req.ProjectUid != "0" {
        // 新逻辑：检查Project级别的createLot权限
        hasPermission = client.IsAllowed(ctx, "", biz.PermCreateLot, biz.PermClsProject, req.ProjectUid)
    } else {
        // 原有逻辑：检查组织级别的create权限
        scope := client.GroupScope(req.OrgUid)
        hasPermission = client.IsAllowed(ctx, "", biz.PermCreate, biz.PermClsLot, scope)
    }
    
    if !hasPermission {
        return nil, errors.NewErrForbidden()
    }
    
    // ... 其余逻辑保持不变 ...
}
```

### 4.3 Lot权限策略创建改造

#### 4.3.1 双轨制策略创建
在`anno/internal/biz/lot.go`中修改Create方法：

```go
func (o *LotsBiz) Create(ctx context.Context, p *Lot) (lot *Lot, err error) {
    // ... 现有创建逻辑 ...
    
    err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        lot, err = o.repo.Create(ctx, p)
        if err != nil {
            return err
        }

        // 双轨制权限策略创建
        var parents []string
        if p.ProjectID > 0 {
            // 新逻辑：Lot的父级是Project
            parents = []string{ProjectScope(kid.StringID(p.ProjectID))}
        } else {
            // 原有逻辑：Lot的父级是组织
            parents = []string{client.GroupScope(p.OrgUid)}
        }
        
        err = client.CreateAccessPolicies(ctx, PermClsLot, p.GetUid(),
            []string{client.UserScope(p.CreatorUid)}, parents)
        if err != nil {
            return err
        }
        
        // ... 其余逻辑保持不变 ...
    })
    
    return lot, err
}

## 5. Project权限管理实现

### 5.1 Project创建权限策略

#### 5.1.1 Project创建时的权限设置
在`anno/internal/biz/project.go`中（如果不存在需要创建）：

```go
func (o *ProjectsBiz) Create(ctx context.Context, p *Project) (project *Project, err error) {
    o.log.Info(ctx, "CreateProject", "project", p)

    err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        project, err = o.repo.Create(ctx, p)
        if err != nil {
            return err
        }

        // 创建Project权限策略
        err = client.CreateAccessPolicies(ctx, PermClsProject, p.GetUid(),
            []string{client.UserScope(p.CreatorUid)},     // 创建者作为owner
            []string{client.GroupScope(p.OrgUid)})        // 所属组织作为parent
        if err != nil {
            return err
        }

        return nil
    })

    return project, err
}
```

### 5.2 权限检查兼容性处理

#### 5.2.1 Lot权限检查的兼容性逻辑
在`anno/internal/service/lot.go`中添加权限检查辅助方法：

```go
// checkLotPermission 检查Lot相关权限（兼容新老逻辑）
func (o *LotsService) checkLotPermission(ctx context.Context, perm, lotUid string, lot *biz.Lot) bool {
    if lot.ProjectID > 0 {
        // 新逻辑：通过Project检查权限
        projectUid := kid.StringID(lot.ProjectID)

        // 检查Project级别权限
        if client.IsAllowed(ctx, "", perm, biz.PermClsProject, projectUid) {
            return true
        }

        // 检查Lot级别权限（Project下的Lot可能有独立权限）
        return client.IsAllowed(ctx, "", perm, biz.PermClsLot, lotUid)
    } else {
        // 原有逻辑：直接检查Lot权限
        return client.IsAllowed(ctx, "", perm, biz.PermClsLot, lotUid)
    }
}

// 在GetLot、UpdateLot、DeleteLot等方法中使用
func (o *LotsService) GetLot(ctx context.Context, req *anno.GetLotRequest) (*anno.Lot, error) {
    lot, err := o.bz.GetByUid(ctx, req.Uid)
    if err != nil {
        return nil, err
    }

    // 使用兼容性权限检查
    if !o.checkLotPermission(ctx, biz.PermGet, req.Uid, lot) {
        return nil, errors.NewErrForbidden()
    }

    return FromBizLot(lot), nil
}
```

## 6. 实施步骤和注意事项

### 6.1 实施步骤

#### 阶段1：权限规则准备
1. **更新静态权限规则**
   - 在`keto/tuples.txt`中添加AnnoProject相关权限规则
   - 执行权限规则更新脚本

2. **更新命名空间定义**
   - 修改`keto/namespaces.keto.ts`，添加AnnoProject命名空间
   - 修改AnnoLot命名空间，支持AnnoProject父级

3. **重启Keto服务**
   - 重新加载权限规则和命名空间定义

#### 阶段2：IAM动态权限支持
1. **新增Project权限管理代码**
   - 在`iam/pkg/keto/`中添加`project.go`
   - 实现Project权限模板和管理方法

2. **修改资源权限模板**
   - 更新`iam/pkg/keto/object.go`，支持AnnoProject父级关系

3. **重启IAM服务**

#### 阶段3：Anno业务逻辑改造
1. **更新权限常量**
   - 在`anno/internal/biz/perms.go`中添加Project相关权限

2. **实现双轨制权限检查**
   - 修改Lot相关的权限检查逻辑
   - 实现兼容性权限检查方法

3. **Project权限策略创建**
   - 实现Project创建时的权限策略生成

4. **重启Anno服务**

#### 阶段4：测试验证
1. **历史数据兼容性测试**
   - 验证project_id=0的历史Lot权限检查正常
   - 验证历史Job权限继承正常

2. **新数据功能测试**
   - 创建新Project，验证权限策略生成
   - 在Project下创建Lot，验证权限继承
   - 验证三层权限检查逻辑

### 6.2 关键注意事项

#### 6.2.1 数据一致性
- **确保project_id字段处理**：所有判断都要考虑project_id为0、NULL、或大于0的情况
- **权限策略一致性**：新创建的Project必须同时创建对应的权限策略
- **父级关系正确性**：Lot的父级关系要根据是否有Project正确设置

#### 6.2.2 性能考虑
- **权限检查性能**：双轨制可能增加权限检查次数，需要监控性能
- **权限规则数量**：新增Project会增加权限规则数量，注意Keto性能
- **缓存策略**：考虑对权限检查结果进行缓存

#### 6.2.3 回滚方案
- **保留原有逻辑**：双轨制设计天然支持回滚，可以通过配置切换
- **权限规则回滚**：保留权限规则更新前的备份
- **数据库回滚**：如果有数据结构变更，准备回滚脚本

## 7. 权限检查流程对比

### 7.1 原有流程（历史数据）
```
用户请求 → 检查Lot权限 → 检查组织权限 → 返回结果
```

### 7.2 新流程（有Project的数据）
```
用户请求 → 检查Project权限 → 检查Lot权限 → 检查组织权限 → 返回结果
```

### 7.3 兼容性流程（推荐实现）
```
用户请求 → 判断是否有Project →
├─ 有Project：检查Project权限 → 检查Lot权限 → 返回结果
└─ 无Project：检查Lot权限 → 检查组织权限 → 返回结果
```

## 8. 风险评估和缓解

### 8.1 主要风险
1. **权限检查逻辑复杂化**：双轨制增加了代码复杂度
2. **性能影响**：可能增加权限检查时间
3. **数据一致性**：新老数据的权限策略可能不一致

### 8.2 缓解措施
1. **充分测试**：覆盖所有权限检查场景
2. **性能监控**：监控权限检查性能，必要时优化
3. **逐步迁移**：可以提供工具让用户主动迁移历史数据到Project模式
4. **文档完善**：详细记录新的权限检查逻辑

## 9. 总结

### 9.1 推荐方案
采用**双轨制兼容方案**，通过project_id是否为0来区分新老逻辑，既保证了历史数据的兼容性，又支持了新的三层权限架构。

### 9.2 核心优势
- 最小化风险，不影响现有功能
- 平滑过渡，支持逐步迁移
- 代码改动相对较小
- 符合现有的权限设计架构

### 9.3 实施建议
建议按照上述阶段逐步实施，每个阶段都要充分测试，确保不影响现有功能的前提下，逐步引入新的Project层级权限管理。
```

### Project层级权限继承架构图
、、、
graph TB
    subgraph "组织层"
        ORG[IamGroup:org123]
    end
    
    subgraph "项目层 (新增)"
        PROJ[AnnoProject:proj456]
    end
    
    subgraph "批次层"
        LOT1[AnnoLot:lot789<br/>有Project]
        LOT2[AnnoLot:lot999<br/>历史数据]
    end
    
    subgraph "任务层"
        JOB1[AnnoJob:job111]
        JOB2[AnnoJob:job222]
    end
    
    subgraph "权限策略"
        POL_ORG[IamPolicy:org123.editor]
        POL_PROJ[IamPolicy:proj456.owner]
        POL_LOT1[IamPolicy:lot789.editor]
        POL_LOT2[IamPolicy:lot999.editor]
    end
    
    subgraph "用户"
        USER[IamUser:alice]
    end
    
    %% 组织关系
    ORG -.->|parents| PROJ
    ORG -.->|policies| POL_ORG
    
    %% 项目关系
    PROJ -.->|parents| LOT1
    PROJ -.->|policies| POL_PROJ
    
    %% 批次关系 - 新逻辑
    LOT1 -.->|parents| JOB1
    LOT1 -.->|policies| POL_LOT1
    
    %% 批次关系 - 历史逻辑
    ORG -.->|parents| LOT2
    LOT2 -.->|parents| JOB2
    LOT2 -.->|policies| POL_LOT2
    
    %% 用户权限
    USER -.->|member of| ORG
    POL_ORG -.->|users| USER
    POL_PROJ -.->|users| USER
    
    %% 权限继承路径
    USER -.->|新路径| PROJ
    PROJ -.->|继承| LOT1
    LOT1 -.->|继承| JOB1
    
    USER -.->|原路径| LOT2
    LOT2 -.->|继承| JOB2
    
    style PROJ fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style LOT1 fill:#e8f5e8
    style LOT2 fill:#fff3e0
    style USER fill:#fce4ec
    style POL_PROJ fill:#f3e5f5
、、、


### Project层级权限继承架构图
、、、
graph TB
    subgraph "组织层"
        ORG[IamGroup:org123]
    end
    
    subgraph "项目层 (新增)"
        PROJ[AnnoProject:proj456]
    end
    
    subgraph "批次层"
        LOT1[AnnoLot:lot789<br/>有Project]
        LOT2[AnnoLot:lot999<br/>历史数据]
    end
    
    subgraph "任务层"
        JOB1[AnnoJob:job111]
        JOB2[AnnoJob:job222]
    end
    
    subgraph "权限策略"
        POL_ORG[IamPolicy:org123.editor]
        POL_PROJ[IamPolicy:proj456.owner]
        POL_LOT1[IamPolicy:lot789.editor]
        POL_LOT2[IamPolicy:lot999.editor]
    end
    
    subgraph "用户"
        USER[IamUser:alice]
    end
    
    %% 组织关系
    ORG -.->|parents| PROJ
    ORG -.->|policies| POL_ORG
    
    %% 项目关系
    PROJ -.->|parents| LOT1
    PROJ -.->|policies| POL_PROJ
    
    %% 批次关系 - 新逻辑
    LOT1 -.->|parents| JOB1
    LOT1 -.->|policies| POL_LOT1
    
    %% 批次关系 - 历史逻辑
    ORG -.->|parents| LOT2
    LOT2 -.->|parents| JOB2
    LOT2 -.->|policies| POL_LOT2
    
    %% 用户权限
    USER -.->|member of| ORG
    POL_ORG -.->|users| USER
    POL_PROJ -.->|users| USER
    
    %% 权限继承路径
    USER -.->|新路径| PROJ
    PROJ -.->|继承| LOT1
    LOT1 -.->|继承| JOB1
    
    USER -.->|原路径| LOT2
    LOT2 -.->|继承| JOB2
    
    style PROJ fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style LOT1 fill:#e8f5e8
    style LOT2 fill:#fff3e0
    style USER fill:#fce4ec
    style POL_PROJ fill:#f3e5f5
、、、