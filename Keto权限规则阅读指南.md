# Keto权限规则阅读指南

## 1. 基本语法结构

```
Namespace:Object#Relation@Subject
```

**阅读原则**：永远从左往右读，理解为 "Object 与 Subject 之间存在 Relation 关系"

## 2. 关系类型详解

### 2.1 拥有关系 (Ownership Relations)

#### members - 成员关系
```
IamGroup:org123#members@IamUser:alice
```
**阅读**：组织org123与用户alice之间存在members关系
**含义**：组织org123**拥有**成员alice
**理解**：alice**属于**org123组织

#### owners - 所有者关系
```
IamUser:alice#owners@IamUser:alice
```
**阅读**：用户alice与用户alice之间存在owners关系
**含义**：用户alice**拥有**所有者alice（自我所有权）
**理解**：alice**拥有**对自己的所有权

#### perms - 权限关系
```
IamRole:admin#<EMAIL>
```
**阅读**：角色admin与权限AnnoLot.create之间存在perms关系
**含义**：角色admin**拥有**权限AnnoLot.create
**理解**：admin角色**具备**AnnoLot.create权限

### 2.2 绑定关系 (Binding Relations)

#### roles - 角色绑定
```
IamPolicy:sys/admin#roles@IamRole:admin
```
**阅读**：策略sys/admin与角色admin之间存在roles关系
**含义**：策略sys/admin**绑定**角色admin
**理解**：策略sys/admin**授予**admin角色

#### users - 用户绑定
```
IamPolicy:sys/admin#users@IamGroup:sys/admin#members
```
**阅读**：策略sys/admin与用户组sys/admin的成员之间存在users关系
**含义**：策略sys/admin**绑定**用户组sys/admin的成员
**理解**：策略sys/admin**适用于**sys/admin组的所有成员

#### policies - 策略绑定
```
IamUser:alice#policies@IamPolicy:sys/admin
```
**阅读**：用户alice与策略sys/admin之间存在policies关系
**含义**：用户alice**绑定**策略sys/admin
**理解**：用户alice**受**策略sys/admin**控制**

### 2.3 层级关系 (Hierarchy Relations)

#### parents - 父级关系
```
AnnoJob:job123#parents@AnnoLot:lot456
```
**阅读**：任务job123与批次lot456之间存在parents关系
**含义**：任务job123**拥有**父级lot456
**理解**：任务job123**属于**批次lot456

```
IamUser:alice#parents@IamGroup:org123
```
**阅读**：用户alice与组织org123之间存在parents关系
**含义**：用户alice**拥有**父级org123
**理解**：用户alice**属于**组织org123

## 3. 常见混淆点解析

### 3.1 策略相关规则

#### ❓ 策略绑定角色
```
IamPolicy:AnnoLot.lot123.owner#roles@IamRole:AnnoLot.owner
```
**正确理解**：策略"AnnoLot.lot123.owner"**绑定了**角色"AnnoLot.owner"
**错误理解**：角色"AnnoLot.owner"属于策略"AnnoLot.lot123.owner"

#### ❓ 策略绑定用户
```
IamPolicy:AnnoLot.lot123.owner#users@IamUser:alice
```
**正确理解**：策略"AnnoLot.lot123.owner"**适用于**用户"alice"
**错误理解**：用户"alice"拥有策略"AnnoLot.lot123.owner"

#### ❓ 资源绑定策略
```
AnnoLot:lot123#policies@IamPolicy:AnnoLot.lot123.owner
```
**正确理解**：资源"lot123"**受**策略"AnnoLot.lot123.owner"**控制**
**错误理解**：策略"AnnoLot.lot123.owner"属于资源"lot123"

### 3.2 成员关系规则

#### ❓ 组织包含成员
```
IamGroup:org123#members@IamUser:alice
```
**正确理解**：组织"org123"**包含**成员"alice"
**错误理解**：用户"alice"拥有组织"org123"

**记忆技巧**：组织在左边，用户在右边，组织"收纳"用户

### 3.3 权限继承规则

#### ❓ 角色继承权限
```
IamRole:admin#perms@IamRole:editor#perms
```
**正确理解**：角色"admin"**继承**角色"editor"的所有权限
**错误理解**：角色"editor"的权限属于角色"admin"

## 4. 阅读技巧总结

### 4.1 通用阅读模式

1. **识别主体**：`#`前面的是主体(Object)
2. **识别客体**：`@`后面的是客体(Subject)  
3. **识别关系**：`#`和`@`之间的是关系(Relation)
4. **理解方向**：根据关系类型确定"谁对谁"的关系

### 4.2 关系方向记忆法

#### 拥有类关系 (主体拥有客体)
- `members`: 组织**拥有**成员
- `perms`: 角色**拥有**权限
- `owners`: 实体**拥有**所有者

#### 绑定类关系 (主体绑定客体)
- `roles`: 策略**绑定**角色
- `users`: 策略**绑定**用户
- `policies`: 实体**绑定**策略

#### 层级类关系 (主体拥有父级)
- `parents`: 子级**拥有**父级

### 4.3 快速判断法

**看关系词确定方向**：
- `members` → 左边包含右边
- `perms` → 左边拥有右边
- `roles` → 左边绑定右边
- `users` → 左边适用于右边
- `policies` → 左边受右边控制
- `parents` → 左边属于右边

## 5. 实际例子练习

### 例子1：用户权限检查
```
IamGroup:org123#members@IamUser:alice
IamGroup:org123#policies@IamPolicy:org123.viewer
IamPolicy:org123.viewer#roles@IamRole:anno.viewer
IamPolicy:org123.viewer#users@IamGroup:org123#members
IamRole:anno.viewer#<EMAIL>
```

**阅读理解**：
1. 组织org123**包含**成员alice
2. 组织org123**受**策略org123.viewer**控制**
3. 策略org123.viewer**绑定**角色anno.viewer
4. 策略org123.viewer**适用于**org123的所有成员
5. 角色anno.viewer**拥有**AnnoLot.list权限

**结论**：alice通过组织成员身份获得AnnoLot.list权限

### 例子2：资源权限创建
```
AnnoLot:lot123#policies@IamPolicy:AnnoLot.lot123.owner
IamPolicy:AnnoLot.lot123.owner#roles@IamRole:AnnoLot.owner
IamPolicy:AnnoLot.lot123.owner#users@IamUser:alice
IamRole:AnnoLot.owner#<EMAIL>
```

**阅读理解**：
1. 资源lot123**受**策略AnnoLot.lot123.owner**控制**
2. 策略AnnoLot.lot123.owner**绑定**角色AnnoLot.owner
3. 策略AnnoLot.lot123.owner**适用于**用户alice
4. 角色AnnoLot.owner**拥有**AnnoLot.update权限

**结论**：alice对lot123拥有update权限

## 6. 记忆口诀

```
左主右客中关系，
方向理解看词义：
members包含要记清，
perms拥有不会错，
policies控制要明白，
parents层级要搞懂。
```

通过这个指南，你应该能够准确理解每个权限规则的含义和方向了！


#### 附：Keto权限规则关系方向图
```mermaid
graph LR
    subgraph "拥有关系 (Ownership)"
        G1[IamGroup:org123] -->|members| U1[IamUser:alice]
        R1[IamRole:admin] -->|perms| P1[AnnoLot.create]
        U2[IamUser:alice] -->|owners| U3[IamUser:alice]
    end
    
    subgraph "绑定关系 (Binding)"
        POL1[IamPolicy:sys/admin] -->|roles| R2[IamRole:admin]
        POL2[IamPolicy:sys/admin] -->|users| G2[IamGroup:sys/admin#members]
        U4[IamUser:alice] -->|policies| POL3[IamPolicy:sys/admin]
    end
    
    subgraph "层级关系 (Hierarchy)"
        J1[AnnoJob:job123] -->|parents| L1[AnnoLot:lot456]
        U5[IamUser:alice] -->|parents| G3[IamGroup:org123]
    end
    
    subgraph "理解方式"
        LEFT[左边主体<br/>Object] -->|关系<br/>Relation| RIGHT[右边客体<br/>Subject]
    end
    
    style G1 fill:#e3f2fd
    style POL1 fill:#fff3e0
    style J1 fill:#f3e5f5
    style LEFT fill:#e8f5e8
    style RIGHT fill:#ffebee
```