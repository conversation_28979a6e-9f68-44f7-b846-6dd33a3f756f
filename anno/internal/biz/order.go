// source: anno/v1/order.proto
package biz

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"time"

	"anno/api/client"
	"anno/internal/mq"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type OrderState string

func (o OrderState) IsFinal() bool  { return o == OrderStateFinished || o == OrderStateCanceled }
func (o OrderState) String() string { return string(o) }

const (
	OrderStateUnspecified  OrderState = "unspecified_state"
	OrderStateInitializing OrderState = "initializing" // preparing data
	OrderStateWaiting      OrderState = "waiting"      // waiting for execution start
	OrderStateOngoing      OrderState = "ongoing"
	OrderStateFinished     OrderState = "finished"
	OrderStateCanceled     OrderState = "canceled"
	OrderStateFailed       OrderState = "failed" // error occurred when parsing data
)

type OrderSource = serial.Type[*anno.Source]

type Order struct {
	ID       int64       `json:"id" gorm:"default:null"`
	Name     string      `json:"name" gorm:"default:null"`
	Size     int32       `json:"size" gorm:"default:null"`
	Source   OrderSource `json:"source" gorm:"default:null"`
	State    OrderState  `json:"state" gorm:"default:null"`
	InsTotal int32       `json:"ins_total" gorm:"default:null"` // include interpolated objects
	Error    string      `json:"error" gorm:"default:null"`

	AnnoResultURL  string `json:"anno_result_url" gorm:"default:null"`
	CanExportAnnos bool   `json:"can_export_annos" gorm:"default:"`

	CreatorUid string    `json:"creator_uid" gorm:"default:null"`
	DataUid    string    `json:"data_uid" gorm:"default:null"`
	OrgUid     string    `json:"org_uid" gorm:"default:null"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"default:null"`
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`
	DeletedAt  DeletedAt `json:"deleted_at" gorm:"default:null"`
}

func (o *Order) GetID() int64 { return o.ID }
func (o *Order) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *Order) GetUid() string { return kid.StringID(o.ID) }
func (o *Order) UidFld() string { panic("code error") }

type OrdersSfld string

func (o OrdersSfld) String() string { return string(o) }

func (o OrdersSfld) WithTable() string { return "orders." + string(o) }

const OrderTableName = "orders"

var (
	order_             = field.RegObject(&Order{})
	OrderUpdatableFlds = field.NewModel(OrderTableName, order_,
		"Name", "State", "Error", "Size", "DataUid", "InsTotal", "AnnoResultURL", "CanExportAnnos")

	OrderSfldID            = OrdersSfld(field.Sname(&order_.ID))
	OrderSfldName          = OrdersSfld(field.Sname(&order_.Name))
	OrderSfldSize          = OrdersSfld(field.Sname(&order_.Size))
	OrderSfldSource        = OrdersSfld(field.Sname(&order_.Source))
	OrderSfldState         = OrdersSfld(field.Sname(&order_.State))
	OrderSfldError         = OrdersSfld(field.Sname(&order_.Error))
	OrderSfldInsTotal      = OrdersSfld(field.Sname(&order_.InsTotal))
	OrderSfldDataUid       = OrdersSfld(field.Sname(&order_.DataUid))
	OrderSfldCreatorUid    = OrdersSfld(field.Sname(&order_.CreatorUid))
	OrderSfldOrgUid        = OrdersSfld(field.Sname(&order_.OrgUid))
	OrderSfldAnnoResultURL = OrdersSfld(field.Sname(&order_.AnnoResultURL))

	OrderSfldCanExportAnnos = OrdersSfld(field.Sname(&order_.CanExportAnnos))
)

type OrderListFilter struct {
	IDs         []int64
	OrgUid      string
	CreatorUid  string
	NamePattern string
	States      []OrderState

	BizgranteeUid string // to check kam's grants
}

type OrdersRepo interface {
	DoTx(ctx context.Context, fn func(ctx context.Context, tx Tx) error) (err error)

	Create(context.Context, *Order) (*Order, error)
	Update(context.Context, *Order, *FieldMask) (*Order, error)
	GetByID(context.Context, int64) (*Order, error)
	DeleteByID(context.Context, int64) error
	List(context.Context, *OrderListFilter, Pager) ([]*Order, error)
	Count(context.Context, *OrderListFilter) (int, error)
}

type OrdersBiz struct {
	repo OrdersRepo
	log  *log.Helper
}

func NewOrdersBiz(repo OrdersRepo, logger log.Logger) *OrdersBiz {
	return &OrdersBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *OrdersBiz) Repo() OrdersRepo { return o.repo }

func (o *OrdersBiz) Create(ctx context.Context, p *Order) (order *Order, err error) {
	o.log.Info(ctx, "CreateOrder", "param", p)
	p.State = OrderStateInitializing

	// create data
	annoSource := p.Source.E
	annofeedSource := &anno.Source{
		Uris: annoSource.GetUris(),
		Proprietary: &anno.Source_Proprietary{
			Type:   annoSource.GetProprietary().GetType(),
			Config: annoSource.GetProprietary().GetConfig(),
		},
		Style:         annoSource.GetStyle(),
		ElemType:      annoSource.GetElemType(),
		IsFrameSeries: annoSource.GetIsFrameSeries(),
		PlainSizeGb:   annoSource.GetPlainSizeGb(),
		ErrorHandlers: annoSource.GetErrorHandlers(),
		AutoParse:     annoSource.GetAutoParse(),
		NamedUris:     annoSource.GetNamedUris(),
		Converter:     annoSource.Converter,
	}
	data, err := client.CreateData(ctx, &client.CreateDataRequest{
		Name:     p.Name,
		Source:   annofeedSource,
		OrgUid:   p.OrgUid,
		OrderUid: "sameid",
	})
	fmt.Println("---> save to annoFeed data.")
	spew.Dump(annofeedSource)
	if err != nil {
		return nil, fmt.Errorf("failed to create data: %w", err)
	}

	err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		p.ID = kid.ParseID(data.Uid) // make order has the same id as data
		p.DataUid = data.Uid
		fmt.Println("---> state - 1 : ", p.State.String())
		order, err = o.repo.Create(ctx, p)
		if err != nil {
			return err
		}
		fmt.Println("---> state - 2 : ", order.State.String())
		err = mq.PublishEvt(ctx, EvtTypeAnnoOrder, EvtSubtypeCreate, order)
		if err != nil {
			return fmt.Errorf("failed to publish order event: %w", err)
		}

		err = client.CreateAccessPolicies(ctx, PermClsOrder, p.GetUid(),
			[]string{client.UserScope(p.CreatorUid)}, []string{client.GroupScope(p.OrgUid)})
		if err != nil {
			return fmt.Errorf("failed to create access policies: %w", err)
		}

		return nil
	})
	if err != nil {
		return nil, err
	}
	return
}

func (o *OrdersBiz) Update(ctx context.Context, p *Order, fldMask *FieldMask) (*Order, error) {
	o.log.Info(ctx, "UpdateOrder", "param", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *OrdersBiz) GetByID(ctx context.Context, id int64) (*Order, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *OrdersBiz) GetByUid(ctx context.Context, uid string) (*Order, error) {
	return o.GetByID(ctx, kid.ParseID(uid))
}

func (o *OrdersBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByIDOrder", "id", id)
	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		if err := o.repo.DeleteByID(ctx, id); err != nil {
			return err
		}
		return client.DeleteAccessPolicies(ctx, PermClsOrder, kid.StringID(id))
	})
}

func (o *OrdersBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUidOrder", "uid", uid)
	return o.DeleteByID(ctx, kid.ParseID(uid))
}

func (o *OrdersBiz) List(ctx context.Context, filter *OrderListFilter, pager Pager) ([]*Order, error) {
	o.log.Info(ctx, "ListOrder", "param", filter)
	return o.repo.List(ctx, filter, pager)
}

func (o *OrdersBiz) Count(ctx context.Context, filter *OrderListFilter) (int, error) {
	return o.repo.Count(ctx, filter)
}

func (o *OrdersBiz) ClearAnnoResult(ctx context.Context, orderID int64) error {
	if orderID <= 0 {
		return nil
	}

	_, err := o.Update(ctx, &Order{ID: orderID}, field.NewMask(string(OrderSfldAnnoResultURL)))
	if err != nil {
		return fmt.Errorf("failed to clear order AnnoResultUrl: %w", err)
	}
	return nil
}
