package keto

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"strings"

	keto "github.com/ory/keto/proto/ory/keto/relation_tuples/v1alpha2"
)

// resource policy template
const (
	resourceTpl = `
# resource owners policy
IamPolicy:<namespace>.<resource-name>.owner#roles@IamRole:<namespace>.owner
<namespace>:<resource-name>#policies@IamPolicy:<namespace>.<resource-name>.owner
<namespace>:<resource-name>#policies@IamPolicy:sys/admin
`
	resourceOwnerUserTpl  = "IamPolicy:<namespace>.<resource-name>.owner#users@IamUser:<subject-name>\n"
	resourceOwnerGroupTpl = "IamPolicy:<namespace>.<resource-name>.owner#users@IamGroup:<subject-name>#members\n"

	resourceParentTpl = "<namespace>:<resource-name>#parents@<parent>\n"
)

func init() {
	// validate templates
	for _, s := range []string{resourceTpl, resourceOwnerUserTpl, resourceOwnerGroupTpl} {
		_, err := ParseTuples(s)
		if err != nil {
			panic(fmt.Errorf("failed to parse resource policy template \n: %v\n: %w", s, err))
		}
	}
}

func (o *AccessMgr) CreateObjects(ctx context.Context, owners, parents []string, objects ...string) (err error) {
	fmt.Println("---> CreateObjects:", owners, parents, objects)
	subb := &strings.Builder{}
	for _, e := range owners {
		ns, id, err := ParseUserGroupName(e)
		fmt.Println("---> own - ns:", ns, ", id:", id)
		if err != nil {
			return err
		}
		tpl := resourceOwnerUserTpl
		if ns == GroupNs {
			tpl = resourceOwnerGroupTpl
		}
		subb.WriteString(strings.ReplaceAll(tpl, "<subject-name>", id))
	}
	for _, parent := range parents {
		ns, _ := ParseObjectName(parent)
		fmt.Println("---> parent - ns:", ns)
		if ns == "" {
			return fmt.Errorf("lack of type in resource parent %v", parent)
		}
		subb.WriteString(strings.ReplaceAll(resourceParentTpl, "<parent>", parent))
	}
	fmt.Println("---> subb:", subb.String())
	subTpl := subb.String()

	tps := make([]*RelationTuple, 0, len(objects)*(len(owners)+3))
	for _, name := range objects {
		ns, id := ParseObjectName(name)
		fmt.Println("---> obj - ns:", ns, ", id:", id)
		if ns == "" {
			return fmt.Errorf("lack of type in resource name %v", name)
		}
		rep := strings.NewReplacer("<namespace>", ns, "<resource-name>", id)
		s := rep.Replace(resourceTpl + subTpl)
		t, err := ParseTuples(s)
		if err != nil {
			return fmt.Errorf("failed to parse resource policy template: %q: %w", s, err)
		}
		tps = append(tps, t...)
	}
	spew.Dump("---> CreateObjects tps: ", tps)
	return o.kt.Add(ctx, tps...)
}

func (o *AccessMgr) IsObjectExist(ctx context.Context, name string) (exist bool, err error) {
	ns, id := ParseObjectName(name)
	if ns == "" {
		return false, fmt.Errorf("lack of type in resource name %v", name)
	}
	rsp, err := o.kt.List(ctx, &keto.ListRelationTuplesRequest{
		RelationQuery: NewQuery(ns, id, "", nil),
	})
	if err != nil {
		return
	}
	return len(rsp.RelationTuples) > 0, nil
}

// DeleteObject deletes the object relations and dedicated policies.
// TODO: do it in a transaction
func (o *AccessMgr) DeleteObject(ctx context.Context, name string) (err error) {
	// delete attached policies
	policies, err := o.GetAttachedPolicies(ctx, name)
	if err != nil {
		return err
	}
	// if len(policies) > 0 {
	// tps := lo.Map(policies, func(v string, _ int) *RelationTuple { return NewTuple(PolicyNs, v, "", nil) })
	for _, policy := range policies {
		if err = o.kt.DeleteObj(ctx, PolicyNs, policy); err != nil {
			return fmt.Errorf("failed to delete attached policies: %w", err)
		}
	}
	// }

	// delete the object
	ns, id := ParseObjectName(name)
	if ns == "" {
		return fmt.Errorf("lack of type in resource name %v", name)
	}
	return o.kt.DeleteObj(ctx, ns, id)
}
