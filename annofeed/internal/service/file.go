// source: annofeedv1/file.proto
package service

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"mime"
	"path"
	"strings"
	"time"

	"annofeed/api/client"
	"annofeed/internal/biz"
	"annofeed/internal/conf"
	"annofeed/internal/signer"

	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/s3"
	"gitlab.rp.konvery.work/platform/pkg/upload"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gosimple/slug"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func FromBizFileState(v int) annofeed.File_State_Enum {
	switch v {
	case biz.FileStateUploading:
		return annofeed.File_State_uploading
	case biz.FileStateUploaded:
		return annofeed.File_State_uploaded
	}
	return annofeed.File_State_unspecified
}

func FromBizFile(d *biz.File) *annofeed.File {
	if d == nil {
		return nil
	}
	o := &annofeed.File{
		Uid:        d.GetUid(),
		Uri:        d.URI,
		Name:       d.Name,
		Size:       float64(d.Size),
		Mime:       d.MIME,
		Sha256:     d.Sha256,
		OrgUid:     d.OrgUid,
		State:      FromBizFileState(d.State),
		CreatorUid: d.CreatorUid,
		CreatedAt:  timestamppb.New(d.CreatedAt),
	}
	return o
}

func ToBizFile(d *annofeed.CreateFileRequest) *biz.File {
	if d == nil {
		return nil
	}
	o := &biz.File{
		Name:   d.Name,
		Size:   int64(d.Size),
		MIME:   d.Mime,
		Sha256: d.Sha256,
		OrgUid: d.OrgUid,
	}
	return o
}

type FilesService struct {
	annofeed.UnimplementedFilesServer
	bz     *biz.FilesBiz
	s3cli  *s3.Client
	log    *log.Helper
	svcURL string
}

func NewFilesService(bz *biz.FilesBiz, fscfg *conf.FileServer, s3cli *s3.Client, logger log.Logger) *FilesService {
	return &FilesService{bz: bz, s3cli: s3cli, svcURL: fscfg.SvcUrl, log: log.NewHelper(logger)}
}

func calFileParts(size, parts int) (int, error) {
	const (
		M = 1024 * 1024
		G = 1024 * M
		T = 1024 * G

		// Amazon S3 multipart upload limits: https://docs.aws.amazon.com/AmazonS3/latest/userguide/qfacts.html
		MaxParts    = 10000
		MinPartSize = 5 * M
		MaxPartSize = 5 * G
	)
	switch {
	case parts == 0 && size == 0:
		return 1, nil
	case parts < 0 || parts > MaxParts:
		return 0, errors.NewErrInvalidField(errors.WithMessage("part count should within [0, 10,000]"),
			errors.WithFields("parts"))
	case size < 0:
		return 0, errors.NewErrInvalidField(errors.WithMessage("non-positive size"))
	case size > MaxPartSize*MaxParts:
		return 0, errors.NewErrUnsupportedField(errors.WithMessage("too big file"))
	case parts > 0:
		if size > parts*MaxPartSize {
			return 0, errors.NewErrUnsupportedField(errors.WithMessage("too few parts"))
		}
		if size > 0 && size < (parts-1)*MinPartSize+1 {
			return 0, errors.NewErrUnsupportedField(errors.WithMessage("too many parts"))
		}
		return parts, nil
	}
	lvls := [][2]int{
		{10 * M, 1},
		{100 * M, 5},
		{1000 * M, 10},
		{100 * G, 100},
	}
	for _, lvl := range lvls {
		if size <= lvl[0] {
			return lvl[1], nil
		}
	}
	return (size + MaxPartSize - 1) / MaxPartSize, nil
}

func checkFileNameType(req *annofeed.CreateFileRequest) (slugName string, err error) {
	if req.Name == "" {
		return "", errors.NewErrEmptyField(errors.WithFields("name"))
	}
	ext := strings.ToLower(path.Ext(req.Name))
	slugName = slug.Make(req.Name[:len(req.Name)-len(ext)]) + ext
	if strings.HasPrefix(req.Mime, ".") {
		ext, req.Mime = req.Mime, ""
	}
	if req.Mime == "" && ext != "" {
		// infer type from ext
		req.Mime = mime.TypeByExtension(ext)
	}
	return
}

func (o *FilesService) CreateFile(ctx context.Context, req *annofeed.CreateFileRequest) (*annofeed.CreateFileReply, error) {
	slugName, err := checkFileNameType(req)
	if err != nil {
		return nil, err
	}
	parts, err := calFileParts(int(req.Size), int(req.Parts))
	fmt.Println("---> cal file parts: ", parts)
	if err != nil {
		return nil, err
	}
	// if req.Sha256 == "" {
	// 	return nil, errors.NewErrInvalid(errors.WithFields("sha256"))
	// }

	op := biz.UserFromCtx(ctx)
	org, scope := getCreateScope(op.User, req.OrgUid)
	_ = scope
	// if org == "" {
	// 	return nil, errors.NewErrEmptyField(errors.WithFields("org"))
	// }
	// if !client.IsAllowed(ctx, "", biz.PermCreate, biz.PermClsFile, scope) {
	// 	return nil, errors.NewErrForbidden()
	// }

	// find same file by sha256
	var data *biz.File
	var uri *upload.URI
	if req.Sha256 != "" {
		files, _, err := o.bz.List(ctx, &biz.FileListFilter{OrgUid: org, Sha256: req.Sha256},
			biz.Pager{Pagesz: 1})
		if err != nil {
			return nil, err
		}
		fmt.Println("---> len files: ", len(files))
		if len(files) > 0 && files[0].Size == int64(req.Size) {
			// find a file
			data = files[0]
			if data.State == biz.FileStateUploaded {
				fmt.Println("---> file is uploaded")
				return &annofeed.CreateFileReply{File: FromBizFile(data), PreExisting: true}, nil
			}
			uri, _ = upload.ParseURI(data.URI)
		}
	}

	preExisting := data != nil
	if !preExisting {
		// create a new file
		data = ToBizFile(req)
		data.ID = kid.NewID()
		fmt.Println(data.ID)
		// generate file URI
		key := biz.MakeUploadFileKey(data.GetUid(), slugName)
		uri = upload.NewS3URI(o.s3cli.DefaultBucket(), key)
		if upload.GetStorage().IsLocalFS() {
			uri = upload.NewLocalfsURI(key)
		}
		data.URI = uri.String()
		fmt.Println(data.URI)

		data.OrgUid = org
		data.CreatorUid = op.GetUid()
		data, err = o.bz.Create(ctx, data)
		if err != nil {
			return nil, err
		}
		reply := &annofeed.CreateFileReply{File: FromBizFile(data), PreExisting: preExisting}
		if upload.GetStorage().IsLocalFS() {
			result, err := upload.Upload(ctx, key, strings.NewReader(""), nil)
			if err != nil {
				fmt.Println("---> localfs err: ", err.Error())
				return nil, err
			}
			spew.Dump("---> localfs: ", result)
			reply.UploadUrls = append(reply.UploadUrls, result.URI)
			return reply, nil
		}
	}

	// generate presigned URLs for upload
	reply := &annofeed.CreateFileReply{File: FromBizFile(data), PreExisting: preExisting}
	if upload.GetStorage().IsLocalFS() {
		reply.UploadUrls = append(reply.UploadUrls, uri.String())
		return reply, nil
	}
	fmt.Println("---> s3 upload start...")
	urls, expires, err := o.generateUploadURLs(ctx, data, uri, parts)
	if err == nil {
		reply.UploadUrls = urls
		reply.UrlExpiresAt = timestamppb.New(expires)
		return reply, nil
	}
	o.log.Errorf("failed to generate upload URLs: %v", err)
	return nil, err
}

func (o *FilesService) generateUploadURLs(ctx context.Context, data *biz.File, uri *upload.URI, parts int) (
	urls []string, expiresAt time.Time, err error) {
	// create presigned upload URLs
	if parts == 1 && data.UploadID == "" {
		fmt.Println("---> single file: ", data.URI)
		// do not do multipart upload
		res, err := o.s3cli.PresignPutObj(ctx, uri.Key(), nil)
		if err != nil {
			fmt.Println("---> presign err: ", err)
			o.log.Errorw("msg", "failed to presign URL for putobj", "error", err)
			return nil, expiresAt, err
		} else {
			return []string{res.URL}, res.ExpiresAt, nil
		}
	}

	// do multipart upload
	res, err := o.s3cli.PresignMultipartUpload(ctx, uri.Key(), &s3.MultipartUploadOpts{
		UploadID:       data.UploadID,
		ContentType:    data.MIME,
		PresignExpires: 24 * time.Hour,
		PartCnt:        parts,
	})
	if err != nil {
		o.log.Errorw("msg", "failed to presign URL for multipart upload", "error", err)
		return nil, expiresAt, err
	}

	fmt.Println("---> s3 upload id: ", res.UploadID)
	// update file to record uploadID
	if data.UploadID == "" {
		data.UploadID = res.UploadID
		_, err = o.bz.Update(ctx, data, field.NewMask(biz.FileSfldUploadID))
		if err != nil {
			o.log.Errorw("msg", "failed to set file uploadID", "error", err)
			return nil, expiresAt, err
		}
	}
	return res.PartURLs, res.ExpiresAt, nil
}

func (o *FilesService) GetFileUploadURLs(ctx context.Context, req *annofeed.GetFileUploadURLsRequest) (
	*annofeed.GetFileUploadURLsReply, error) {
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsFile, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if data.State == biz.FileStateUploaded {
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("file already uploaded"))
	}
	parts, err := calFileParts(int(data.Size), int(req.Parts))
	if err != nil {
		return nil, err
	}
	uri, err := upload.ParseURI(data.URI)
	if err != nil {
		return nil, fmt.Errorf("failed to parse file URI %v: %w", data.URI, err)
	}

	urls, expires, err := o.generateUploadURLs(ctx, data, uri, parts)
	if err != nil {
		return nil, err
	}
	return &annofeed.GetFileUploadURLsReply{UploadUrls: urls, UrlExpiresAt: timestamppb.New(expires)}, nil
}

func (o *FilesService) UpdateFile(ctx context.Context, req *annofeed.UpdateFileRequest) (*annofeed.File, error) {
	if len(req.Fields) != 1 || req.Fields[0] != biz.FileSfldName {
		return nil, errors.NewErrForbidden(errors.WithMessage("only allow to change name"))
	}
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsFile, req.File.Uid) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.Update(ctx, &biz.File{ID: kid.ParseID(req.File.Uid), Name: req.File.Name},
		field.NewMask(req.Fields...))

	return FromBizFile(data), err
}

func (o *FilesService) FinishFileUpload(ctx context.Context, req *annofeed.FinishFileUploadRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsFile, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if data.State == biz.FileStateUploaded {
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("file already uploaded"))
	}
	if data.UploadID != "" {
		if len(req.Etags) == 0 {
			return nil, errors.NewErrEmptyField(errors.WithFields("etags"))
		}
		uri, err := upload.ParseURI(data.URI)
		if err != nil {
			return nil, fmt.Errorf("failed to parse file URI %v: %w", data.URI, err)
		}
		err = o.s3cli.CompleteMultipartUpload(ctx, uri.Key(), req.Etags, &s3.MultipartUploadOpts{
			Bucket:   uri.Bucket(),
			UploadID: data.UploadID,
		})
		if err != nil {
			return nil, err
		}
	}

	data.State = biz.FileStateUploaded
	_, err = o.bz.Update(ctx, data, field.NewMask(biz.DataSfldState.String()))

	return &emptypb.Empty{}, err
}

func (o *FilesService) DeleteFile(ctx context.Context, req *annofeed.DeleteFileRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermDelete, biz.PermClsFile, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if data.State != biz.FileStateUploaded && data.UploadID != "" {
		uri, err := upload.ParseURI(data.URI)
		if err != nil {
			return nil, fmt.Errorf("failed to parse file URI %v: %w", data.URI, err)
		}
		err = o.s3cli.AbortMultipartUpload(ctx, uri.Key(), &s3.MultipartUploadOpts{
			Bucket:   uri.Bucket(),
			UploadID: data.UploadID,
		})
		if err != nil {
			return nil, err
		}
	}

	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Uid)
}

func (o *FilesService) GetFile(ctx context.Context, req *annofeed.GetFileRequest) (*annofeed.File, error) {
	if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsFile, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.GetByUid(ctx, req.Uid)
	return FromBizFile(data), err
}

func (o *FilesService) ListFile(ctx context.Context, req *annofeed.ListFileRequest) (*annofeed.ListFileReply, error) {
	creator, scope := getListScope(ctx, req.OrgUid, req.CreatorUid)
	if !client.IsAllowed(ctx, "", biz.PermList, biz.PermClsFile, scope) {
		return nil, errors.NewErrForbidden()
	}

	pager := biz.Pager{
		Pagesz:    int(req.Pagesz),
		PageToken: biz.PageToken(req.PageToken),
	}
	filter := &biz.FileListFilter{
		OrgUid:      req.OrgUid,
		CreatorUid:  creator,
		NamePattern: req.NamePattern,
		URIs:        req.Uris,
	}

	// var cnt int
	// if pager.Page == 0 {
	// 	var err error
	// 	cnt, err = o.bz.Count(ctx, filter)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// }
	datas, nextPageToken, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &annofeed.ListFileReply{Files: kslice.Map(FromBizFile, datas), NextPageToken: nextPageToken.String()}, err
}

func (o *FilesService) ShareFile(ctx context.Context, req *annofeed.ShareFileRequest) (*annofeed.ShareFileReply, error) {
	if req.Timeout > 7*24*3600 {
		return nil, errors.NewErrInvalidField(errors.WithMessage("timeout must be less than a week (604800 seconds)"),
			errors.WithFields("timeout"))
	}

	op := biz.UserFromCtx(ctx)
	if !client.IsPrivileged(op.GetRole()) && !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsFile, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if req.Timeout == -1 {
		// returns the permanent URL
		if data.ShareID == "" {
			// generate an unguessable share ID
			data.ShareID = kid.NewRandUid()
			_, err = o.bz.Update(ctx, data, field.NewMask(biz.FileSfldShareID))
			if err != nil {
				return nil, err
			}
		}
		return &annofeed.ShareFileReply{Url: o.svcURL + biz.FileURLPathPrefix + data.ShareID}, nil
	}

	uri, err := upload.ParseURI(data.URI)
	if err != nil {
		return nil, fmt.Errorf("failed to parse file URI %v: %w", data.URI, err)
	}
	expires := time.Duration(req.Timeout) * time.Second
	if uri.Scheme == upload.StorageTypeS3 {
		res, err := signer.SignGetURL(ctx, data.URI, expires)
		if err != nil {
			return nil, err
		}
		return &annofeed.ShareFileReply{Url: res.URL, ExpiresAt: timestamppb.New(res.ExpiresAt)}, nil
	} else {
		return &annofeed.ShareFileReply{Url: o.svcURL + uri.Path}, nil
	}
}
