package lotwf

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"time"

	"anno/api/client"
	"anno/internal/biz"
	"anno/workflow/common"

	"gitlab.rp.konvery.work/platform/pkg/wf"
	"gitlab.rp.konvery.work/platform/pkg/wf/tracing"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
	"go.temporal.io/sdk/workflow"
)

func GetLotWfID(lotID int64) string      { return "anno-lot-" + (&biz.Lot{ID: lotID}).GetUid() }
func GetLotStartWfID(lotID int64) string { return "anno-lotstart-" + (&biz.Lot{ID: lotID}).GetUid() }

func SignalLotWf(ctx context.Context, lotID int64, ev *common.Event) error {
	opts := &wf.WfArgsOpts{
		Workflow:     LotWorkflow,
		WorkflowArgs: []any{lotID},
		Options: wf.StartWorkflowOptions{
			RetryPolicy: wfutil.RetryPolicy(100),
		},
	}
	wfID := GetLotWfID(lotID)
	_, err := wf.SignalWithStartWorkflow(ctx, wfID, "", ev, opts)
	return err
}

func LotStartWorkflow(ctx workflow.Context, lotID int64) (err error) {
	fmt.Println("---> lotStartWorkflow begin.")
	defer func() {
		err = wfutil.RecreateNonRetryableError(err)
	}()

	traceID := GetLotStartWfID(lotID)
	tracing.PrintTraceID(ctx, traceID)
	ctx = tracing.WithCustomTraceID(ctx, traceID)

	var a *Activities
	retry := wfutil.RetryPolicy(10000)
	for {
		state, _ := wfutil.ExecActivityGetResult[string](ctx, a.LotGetDataState, "", 10*time.Second, 0, retry, lotID)
		spew.Dump("lot state: ", state)
		if state == client.DataStateReady.String() {
			break
		}
		if state == biz.LotStateCanceled.String() {
			return nil
		}
		workflow.Sleep(ctx, 10*time.Minute)
	}
	//err = wfutil.ExecActivity(ctx, a.LotCreateJobs, "", 6*time.Hour, 30*time.Minute, retry, lotID)
	err = wfutil.ExecActivity(ctx, a.LotCreateJobs, "", 6*time.Hour, 60*time.Second, retry, lotID)
	if err != nil {
		return
	}
	err = wfutil.ExecActivity(ctx, a.LotInitJobs, "", time.Hour, 10*time.Second, retry, lotID)
	if err != nil {
		return
	}
	err = wfutil.ExecActivity(ctx, a.LotFillExecutors, "", 10*time.Minute, 10*time.Second, retry, lotID)
	if err != nil {
		return
	}
	wfutil.ExecActivity(ctx, a.LotMarkJobReady, "", 10*time.Second, 0, retry, lotID)
	fmt.Println("---> lotStartWorkflow end")
	return
}

func LotWorkflow(ctx workflow.Context, lotID int64) (err error) {
	spew.Dump("---> lot work flow: ", lotID)
	defer func() {
		err = wfutil.RecreateNonRetryableError(err)
	}()

	var a *Activities
	retry := wfutil.RetryPolicy(10000)
	traceID := GetLotWfID(lotID)
	ctx = tracing.WithCustomTraceID(ctx, traceID)
	sigChan := wf.SignalChannel(ctx, "")

	for {
		ev := &common.Event{}
		if ok, _ := sigChan.ReceiveWithTimeout(ctx, time.Nanosecond, ev); !ok {
			// Before completing the Workflow or using Continue-As-New, make sure to do an asynchronous drain
			// on the Signal channel. Otherwise, the Signals will be lost.
			// ReceiveAsync will cause some signals redelivered when there are multiple signals to handle.
			// ReceiveWithTimeout (AwaitWithTimeout + ReceiveAsync) will avoid it.
			return
		}

		tracing.PrintTraceID(tracing.WithCustomTraceID(ctx, ev.TraceID), traceID)
		switch ev.Event {
		case common.EvtLotCreated:
			fmt.Println("---> lot created.")
			wfutil.ExecActivity(ctx, a.LotOnCreated, "", 10*time.Second, 0, retry, lotID)
		case common.EvtConfigChannged:
		case common.EvtLotCanceled:
			wfutil.ExecActivity(ctx, a.LotOnEnded, "", 10*time.Second, 0, retry, lotID, ev)
		case common.EvtLotRevertJobs:
			wfutil.ExecActivity(ctx, a.LotRevertJobs, "", time.Hour, 10*time.Second, retry, lotID, ev)

		case common.EvtJobCompleted:
			finished, _ := wfutil.ExecActivityGetResult[bool](ctx, a.LotIsAllJobsFinished, "", 30*time.Second, 0, retry, lotID)
			if !finished {
				continue
			}
			wfutil.ExecActivity(ctx, a.LotChangeState, "", 30*time.Second, 0, retry, lotID, biz.LotStateFinished)
			ev := &common.Event{Event: common.EvtLotCompleted}
			wfutil.ExecActivity(ctx, a.LotOnEnded, "", 10*time.Second, 0, retry, lotID, ev)
			wfutil.ExecActivity(ctx, a.LotKickAnnout, "", 30*time.Second, 0, retry, lotID)
		case common.EvtLotStarted:
			fmt.Println("---> lot started")
			spew.Dump(a.LotIsJobReady)
			jobReady, _ := wfutil.ExecActivityGetResult[bool](ctx, a.LotIsJobReady, "", 10*time.Second, 0, retry, lotID)
			spew.Dump("---> job start ready: ", jobReady)
			if jobReady {
				wfutil.ExecActivity(ctx, a.LotSyncState, "", 30*time.Second, 0, retry, lotID, biz.LotStateOngoing)
				continue
			}
			// block until LotStartWorkflow finishes to avoid unexpected state transition
			wfutil.ExecChildWf(ctx, LotStartWorkflow, GetLotStartWfID(lotID), "", 0, retry, lotID)
		case common.EvtLotPaused:
			wfutil.ExecActivity(ctx, a.LotSyncState, "", 30*time.Second, 0, retry, lotID, biz.LotStatePaused)
		default:
			// ignore other events
		}
	}
}
