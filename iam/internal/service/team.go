// source: gitlab.rp.konvery.work/platform/apis/iam/v1/team.proto
package service

import (
	"context"
	"fmt"

	"gitlab.rp.konvery.work/platform/pkg/container/kslice"

	"iam/internal/biz"
	"iam/pkg/keto"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"

	"gitlab.rp.konvery.work/platform/apis/iam/v1"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func FromBizTeamType(d int) iam.Team_Type_Enum {
	return iam.Team_Type_Enum(d)
}

func ToBizTeamType(d iam.Team_Type_Enum) int {
	return int(d)
}

func FromBizTeam(d *biz.Team) *iam.Team {
	if d == nil {
		return nil
	}
	o := &iam.Team{
		Uid:    d.GetUid(),
		Name:   d.Name,
		Desc:   d.Desc,
		Avatar: d.Avatar,
		Type:   FromBizTeamType(d.Type),
		// Hierarchy: d.Hierarchy,
		Province:  d.Province,
		City:      d.City,
		CreatedAt: timestamppb.New(d.CreatedAt),
		// Hierarchy: strings.Join(d.GetHierarchy(), "/"),
	}
	return o
}

func ToBaseUser(d *biz.Team) *iam.BaseUser {
	if d == nil {
		return nil
	}
	o := &iam.BaseUser{
		Uid:    d.GetUid(),
		Name:   d.Name,
		Avatar: d.Avatar,
	}
	return o
}

func ToBizTeam(d *iam.CreateTeamRequest) *biz.Team {
	if d == nil {
		return nil
	}
	// loc := &v1.Location{}
	// if d.Location != nil {
	// 	loc = d.Location
	// }
	o := &biz.Team{
		// Uid:    d.Uid,
		ID:     kid.ParseID(d.Uid),
		Name:   d.Name,
		Desc:   d.Desc,
		Avatar: d.Avatar,
		// Hierarchy: d.Hierarchy,
		Province: d.Province,
		City:     d.City,
		Type:     ToBizTeamType(d.Type),
	}
	return o
}

// func BizUserToTeamMember(d *biz.User) *v1.ListMembersReply_Member {
// 	if d == nil {
// 		return nil
// 	}
// 	return &v1.ListMembersReply_Member{
// 		Uid:    d.Uid,
// 		Name:   d.Name,
// 		Avatar: d.Avatar,
// 		Role:   d.Role,
// 	}
// }

type TeamsService struct {
	iam.UnimplementedTeamsServer
	bz     *biz.TeamsBiz
	userbz *biz.UsersBiz
}

func NewTeamsService(bz *biz.TeamsBiz, userbz *biz.UsersBiz) *TeamsService {
	return &TeamsService{bz: bz, userbz: userbz}
}

func (o *TeamsService) CreateTeam(ctx context.Context, req *iam.CreateTeamRequest) (rsp *iam.Team, err error) {
	op := biz.UserFromCtx(ctx)
	var owner *biz.User
	if req.Owner != "" {
		idType, user, err := o.userbz.GetBy(ctx, req.Owner)
		if err != nil {
			if !errors.IsNotFound(err) || (idType != biz.IDTypePhone && idType != biz.IDTypeEmail) {
				return nil, err
			}

			// the user does not exist yet, try to create it
			if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermCreate, keto.MakeUserName("")) {
				return nil, errors.NewErrForbidden()
			}
			_, id := biz.ParseUserIdentity(req.Owner)
			user = &biz.User{Role: biz.TeamRoleMember}
			switch idType {
			case biz.IDTypePhone:
				user.Phone = id
			case biz.IDTypeEmail:
				user.Email = id
			}
			user, err = o.userbz.Create(ctx, user)
			if err != nil {
				return nil, err
			}
		}
		owner = user
	}

	parentID := kid.ParseID(req.ParentUid)
	team := ToBizTeam(req)
	team.CreatorID = op.ID
	// if !op.IsPrivileged() {
	// 	if owner == nil {
	// 		owner = op.User
	// 	}
	// 	if owner.OrgID != 0 && parentID == 0 {
	// 		parentID = owner.OrgID
	// 	}
	// }

	scope := ""
	var parent *biz.Team
	if parentID != 0 {
		parent, err = o.bz.GetByID(ctx, parentID)
		if err != nil {
			return nil, err
		}

		// do not allow nested subteams
		if !parent.IsOrg() {
			return nil, errors.NewErrFailedPrecondition(errors.WithMessage("cannot create team within subteam"))
		}

		// org cannot have more than 100 subteams
		cnt, err := o.bz.Count(ctx, &biz.TeamListFilter{ParentID: &parentID})
		if err != nil {
			return nil, err
		}
		if cnt > 100 {
			return nil, errors.NewErrFailedPrecondition(errors.WithMessage("too many subteams in the orgnization"))
		}

		team.Hierarchy = append(parent.Hierarchy, parent.ID)
		team.ParentID = parent.ID
		scope = parent.GetUid()
	}

	// cannot move users between organizations directly
	if owner != nil {
		if team.IsOrg() && owner.OrgID != 0 {
			return nil, errors.NewErrFailedPreConditionNotEmpty(
				errors.WithMessage("cannot move users between organizations"))
		} else if !team.IsOrg() && owner.OrgID != team.ParentID {
			return nil, errors.NewErrFailedPreConditionNotMatch(
				errors.WithMessage("user is not in the same organization"))
		}
	}
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermCreate, keto.MakeGroupName(scope)) {
		return nil, errors.NewErrForbidden()
	}

	team, err = o.bz.Create(ctx, team, owner, parent)
	return FromBizTeam(team), err
}

func (o *TeamsService) UpdateTeam(ctx context.Context, req *iam.UpdateTeamRequest) (*iam.Team, error) {
	// team, err := o.bz.GetByUid(ctx, req.Team.Uid)
	// if err != nil {
	// 	return nil, err
	// }
	// TODO: filter fields
	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermUpdate, keto.MakeGroupName(req.Team.Uid)) {
		return nil, errors.NewErrForbidden()
	}

	// team = &biz.Team{Uid: req.Uid}
	// if err = util.MapToStruct(req.Fields, team, nil); err != nil {
	// 	return nil, perrors.NewErrBadRequest("unsupported fields")
	// }
	team, err := o.bz.Update(ctx, ToBizTeam(req.Team), field.NewMask(req.Fields...))
	return FromBizTeam(team), err
}

func (o *TeamsService) DeleteTeam(ctx context.Context, req *iam.DeleteTeamRequest) (*emptypb.Empty, error) {
	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermDelete, keto.MakeGroupName(req.Uid)) {
		return nil, errors.NewErrForbidden()
	}

	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Uid)
}

func (o *TeamsService) GetTeam(ctx context.Context, req *iam.GetTeamRequest) (*iam.Team, error) {
	if biz.UserFromCtx(ctx) == nil {
		return nil, errors.NewErrUnauthorized()
	}
	team, err := o.bz.GetByUid(ctx, req.Uid)
	return FromBizTeam(team), err
}

// func (o *TeamsService) GetTeamHierarchy(ctx context.Context, req *v1.GetTeamHierarchyRequest) (*v1.ListTeamReply, error) {
// 	if req.LevelsUp < 0 {
// 		return nil, perrors.NewErrBadRequest("negative levels")
// 	}
// 	if biz.UserFromCtx(ctx) == nil {
// 		return nil, errors.Unauthorized(biz.ErrUnauthorized, "")
// 	}
// 	team, err := o.bz.GetByUid(ctx, req.Uid)
// 	if err != nil {
// 		return nil, err
// 	}
// 	n, hcnt := int(req.LevelsUp), len(team.Hierarchy)
// 	if n == 0 || n > hcnt {
// 		n = hcnt
// 	}
// 	teams := make([]*biz.Team, 0, n+1)
// 	for i := hcnt - n; i < hcnt; i++ {
// 		x, err := o.bz.GetByID(ctx, team.Hierarchy[i])
// 		if err != nil {
// 			return nil, err
// 		}
// 		teams = append(teams, x)
// 	}
// 	if req.IncludeSelf {
// 		teams = append(teams, team)
// 	}

// 	return &v1.ListTeamReply{Teams: util.Map(FromBizTeam, teams)}, nil
// }

func (o *TeamsService) GetTeamsRoot(ctx context.Context, req *iam.GetTeamsRootRequest) (*iam.ListTeamReply, error) {
	if len(req.Uids) == 0 {
		return &iam.ListTeamReply{}, nil
	}
	if len(req.Uids) > 100 {
		return nil, errors.NewErrTooManyItemsInField(errors.WithMessage("too many uids"),
			errors.WithFields("uids"))
	}

	// TODO: should check each team and its root teams' get permissions instead of list permission?
	// op := biz.UserFromCtx(ctx)
	// if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermList, keto.MakeGroupName("")) {
	// 	return nil, errors.NewErrForbidden()
	// }

	// load teams
	teams, err := o.bz.List(ctx, &biz.TeamListFilter{IDs: biz.GetIDs(req.Uids), ParentID: new(int64)})
	if err != nil {
		return nil, err
	}

	// prepare root team IDs
	rootIDMap := make(map[string]int64, len(teams))
	rootIDs := make([]int64, 0, len(teams))
	for _, t := range teams {
		if len(t.Hierarchy) > 0 {
			id := t.Hierarchy[0]
			rootIDs = append(rootIDs, id)
			rootIDMap[t.GetUid()] = id
		}
	}

	// load root teams
	var rootMap map[int64]*biz.Team
	if len(rootIDs) > 0 {
		roots, err := o.bz.ListByIDs(ctx, rootIDs)
		if err != nil {
			return nil, err
		}
		rootMap = kslice.MakeMap(func(t *biz.Team) (int64, *biz.Team) { return t.ID, t }, roots)
	}

	// provide root teams in the request's order
	teamMap := kslice.MakeMap(func(t *biz.Team) (string, *biz.Team) { return t.GetUid(), t }, teams)
	roots := make([]*biz.Team, len(req.Uids))
	for i, uid := range req.Uids {
		if rid := rootIDMap[uid]; rid == 0 {
			roots[i] = teamMap[uid]
		} else {
			roots[i] = rootMap[rid]
		}
	}
	return &iam.ListTeamReply{Total: int32(len(req.Uids)), Teams: kslice.Map(FromBizTeam, roots)}, err
}

func (o *TeamsService) ListTeamByIDs(ctx context.Context, req *iam.ListTeamByIDsRequest) (*iam.ListTeamReply, error) {
	if len(req.Ids) == 0 {
		return &iam.ListTeamReply{}, nil
	}
	if len(req.Ids) > 100 {
		return nil, errors.NewErrTooManyItemsInField(errors.WithMessage("too many ids"),
			errors.WithFields("ids"))
	}

	// TODO: should check each team's get permissions instead of list permission?

	// load teams
	teams, err := o.bz.ListByIDs(ctx, req.Ids)
	if err != nil {
		return nil, err
	}
	return &iam.ListTeamReply{Total: int32(len(req.Ids)), Teams: kslice.Map(FromBizTeam, teams)}, err
}

func (o *TeamsService) ListTeam(ctx context.Context, req *iam.ListTeamRequest) (*iam.ListTeamReply, error) {
	var parentID int64
	scope := ""
	if req.ParentUid != "" {
		scope = req.ParentUid
		parentID = kid.ParseID(scope)
	}
	// TODO: should check each team's get permissions instead of list permission when specified req.Uids?
	op := biz.UserFromCtx(ctx)
	if !biz.IsPrivilegedUser(op.User) &&
		!keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermList, keto.MakeGroupName(scope)) {
		return nil, errors.NewErrForbidden()
	}

	filter := &biz.TeamListFilter{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
		IDs:    biz.GetIDs(req.Uids),

		ParentID:    &parentID,
		NamePattern: req.NamePattern,
		TeamType:    ToBizTeamType(req.TeamType),
	}
	var cnt int64
	if filter.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	teams, err := o.bz.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return &iam.ListTeamReply{Total: int32(cnt), Teams: kslice.Map(FromBizTeam, teams)}, err
}

func (o *TeamsService) ListMembers(ctx context.Context, req *iam.ListMembersRequest) (*iam.ListMembersReply, error) {
	op := biz.UserFromCtx(ctx)
	if !biz.CanManageOrg(op.User, kid.ParseID(req.Uid)) &&
		!keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermListMember, keto.MakeGroupName(req.Uid)) {
		return nil, errors.NewErrForbidden()
	}

	filter := &biz.MemberListFilter{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
		Role:   req.Role,

		NamePattern: req.NamePattern,
	}

	team, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	var cnt int
	if filter.Page == 0 {
		cnt, err = o.bz.CountMembers(ctx, team.ID, filter)
		if err != nil {
			return nil, err
		}
	}
	users, err := o.bz.ListMembers(ctx, team.ID, filter)
	if err != nil {
		return nil, err
	}
	return &iam.ListMembersReply{Total: int32(cnt), Members: kslice.Map(FromBizUser, users), Team: ToBaseUser(team)}, err
}

func (o *TeamsService) AddMembers(ctx context.Context, req *iam.AddMembersRequest) (*emptypb.Empty, error) {
	var userIDs []int64
	userUids, phones, emails := []string{}, []string{}, []string{}
	switch req.IdType {
	case iam.IDType_email:
		emails = req.Identities
	case iam.IDType_phone:
		phones = req.Identities
	case iam.IDType_uid:
		userUids = req.Identities
		userIDs = biz.GetIDs(userUids)
	default:
		return nil, errors.NewErrInvalidField(errors.WithFields("id_type"))
	}

	reqCnt := len(userUids) + len(phones) + len(emails)
	if reqCnt == 0 {
		return nil, errors.NewErrEmptyField(errors.WithFields("users"))
	}
	if reqCnt > 100 {
		return nil, errors.NewErrTooManyItemsInField(errors.WithMessage("too many items"),
			errors.WithFields("user_uids", "phones", "emails"))
	}

	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermAddMember, keto.MakeGroupName(req.Uid)) {
		return nil, errors.NewErrForbidden()
	}
	if op.Role != biz.TeamRoleOwner && !op.IsPrivileged() {
		// only owners and privileged users can set member role
		req.Role = biz.TeamRoleMember
	}

	team, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	var users []*biz.User
	if req.IdType != iam.IDType_uid || team.IsOrg() {
		users, err = o.userbz.List(ctx, &biz.UserListFilter{
			IDs:    biz.GetIDs(userUids),
			Phones: phones,
			Emails: emails,
		})
		if err != nil {
			return nil, err
		}
		if len(users) < reqCnt {
			return nil, errors.NewErrNotFound(errors.WithMessage("some users are not found"),
				errors.WithModel("user"))
		}
		userIDs = biz.GetModelsID(users)
	}
	// cannot move users between organizations directly
	for _, user := range users {
		if team.IsOrg() && user.OrgID != 0 && user.OrgID != team.ID {
			return nil, errors.NewErrFailedPreConditionNotEmpty(
				errors.WithMessage("cannot move users between organizations"))
		} else if !team.IsOrg() && user.OrgID != team.ParentID {
			return nil, errors.NewErrFailedPreConditionNotMatch(
				errors.WithMessage("user is not in the same organization"))
		}
	}
	fmt.Println("---> addMembers userIDs: ", userIDs)
	fmt.Printf("---> team: %v\n", team)
	err = o.bz.AddMembers(ctx, team, req.Role, userIDs...)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, err
}

func (o *TeamsService) DeleteMembers(ctx context.Context, req *iam.DeleteMembersRequest) (*emptypb.Empty, error) {
	if len(req.UserUids) == 0 {
		return nil, errors.NewErrEmptyField(errors.WithFields("user_uids"))
	}
	if len(req.UserUids) > 100 {
		return nil, errors.NewErrTooManyItemsInField(errors.WithMessage("too many user_uids"),
			errors.WithFields("user_uids"))
	}

	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermDeleteMember, keto.MakeGroupName(req.Uid)) {
		return nil, errors.NewErrForbidden()
	}

	team, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}

	userIDs := biz.GetIDs(req.UserUids)
	err = o.bz.DeleteMembers(ctx, team, userIDs)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, err
}

func (o *TeamsService) SetMembersRole(ctx context.Context, req *iam.SetMembersRoleRequest) (*emptypb.Empty, error) {
	if len(req.UserUids) == 0 {
		return nil, errors.NewErrEmptyField(errors.WithFields("user_uids"))
	}
	if len(req.UserUids) > 100 {
		return nil, errors.NewErrTooManyItemsInField(errors.WithMessage("too many user_uids"),
			errors.WithFields("user_uids"))
	}
	if !lo.Contains(biz.TeamRoles(), req.Role) {
		return nil, errors.NewErrInvalidField(errors.WithFields("role"))
	}

	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermSetMemberRole, keto.MakeGroupName(req.Uid)) {
		return nil, errors.NewErrForbidden()
	}

	team, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	err = o.bz.SetMembersRole(ctx, team, req.Role, biz.GetIDs(req.UserUids)...)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, err
}
