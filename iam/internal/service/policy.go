// source: gitlab.rp.konvery.work/platform/apis/iam/v1/policy.proto
package service

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"

	"iam/internal/biz"
	"iam/pkg/keto"

	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/pkg/errors"

	"google.golang.org/protobuf/types/known/emptypb"
)

// func FromBizPolicy(d *biz.Policy) *v1.Policy {
// 	if d == nil {
// 		return nil
// 	}
// 	o := &v1.Policy{
// 		Name:        d.Name,
// 		DisplayName: d.DisplayName,
// 		Roles:       d.Roles,
// 		Users:       d.Users,
// 	}
// 	return o
// }

// func ToBizPolicy(d *v1.Policy) *biz.Policy {
// 	if d == nil {
// 		return nil
// 	}
// 	o := &biz.Policy{
// 		Name:        d.Name,
// 		DisplayName: d.DisplayName,
// 		Roles:       d.Roles,
// 		Users:       d.Users,
// 	}
// 	return o
// }

type PoliciesService struct {
	iam.UnimplementedPoliciesServer
	// bz *biz.PoliciesBiz
}

func NewPoliciesService() *PoliciesService {
	return &PoliciesService{}
}

func (o *PoliciesService) CreatePolicy(ctx context.Context, req *iam.CreatePolicyRequest) (*iam.Policy, error) {
	fmt.Println("---> Iam CreatePolicy.")
	spew.Dump(req)
	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermSetPolicy, req.Resource) {
		return nil, errors.NewErrForbidden()
	}

	name, err := keto.DefaultAccessMgr().CreatePolicy(ctx, req.Resource, req.Role, req.Users)
	if err != nil {
		return nil, err
	}
	return &iam.Policy{
		Name:  name,
		Role:  req.Role,
		Users: req.Users,
	}, nil
}

func (o *PoliciesService) UpdatePolicy(ctx context.Context, req *iam.UpdatePolicyRequest) (*emptypb.Empty, error) {
	fmt.Println("---> Iam UpdatePolicy.")
	spew.Dump(req)
	req.Name = keto.EscapeName(req.Name)
	if err := o.requireResourcePermSetPolicy(ctx, req.Name); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, keto.DefaultAccessMgr().EditPolicy(ctx, req.Name, req.Users)
}

func (o *PoliciesService) DeletePolicy(ctx context.Context, req *iam.DeletePolicyRequest) (*emptypb.Empty, error) {
	req.Name = keto.EscapeName(req.Name)
	if err := o.requireResourcePermSetPolicy(ctx, req.Name); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, keto.DefaultAccessMgr().DeletePolicy(ctx, req.Name)
}

func (o *PoliciesService) requireResourcePermSetPolicy(ctx context.Context, policy string) error {
	// query the policy attached resource
	resources, err := keto.DefaultAccessMgr().GetPolicyAttachedResources(ctx, policy)
	fmt.Println("---> old resources: ", resources)
	if err != nil {
		return err
	}
	if len(resources) != 1 {
		return fmt.Errorf("can only update a policy attached to one resource. policy=%v, attaches=%v",
			policy, len(resources))
	}

	// check resource permission
	resource := resources[0]
	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermSetPolicy, resource) {
		return errors.NewErrForbidden()
	}
	return nil
}

func (o *PoliciesService) GetPolicy(ctx context.Context, req *iam.GetPolicyRequest) (*iam.Policy, error) {
	req.Name = keto.EscapeName(req.Name)
	policy, err := keto.DefaultAccessMgr().GetPolicy(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	if policy == nil {
		return nil, errors.NewErrNotFound(errors.WithModel("Policy"))
	}
	return &iam.Policy{Name: req.Name, Role: policy.Role, Users: policy.Users}, err
}

// func (o *PoliciesService) ListPolicy(ctx context.Context, req *v1.ListPolicyRequest) (*v1.ListPolicyReply, error) {
// 	pager := biz.Pager{
// 		Pagesz:    int(req.Pagesz),
// 		PageToken: req.PageToken,
// 	}
// 	filter := &biz.PolicyListFilter{
// 		// Names:   req.Uids,
// 		// OrgUid:      req.OrgUid,
// 		// NamePattern: req.NamePattern,
// 	}

// 	datas, nextPageToken, err := o.bz.List(ctx, filter, pager)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return &v1.ListPolicyReply{NextPageToken: nextPageToken, Policies: util.Map(FromBizPolicy, datas)}, err
// }

// func (o *PoliciesService) AttachPolicyTo(ctx context.Context, req *v1.AttachPolicyRequest) (*emptypb.Empty, error) {
// 	return nil, keto.DefaultAccessMgr().AttachPolicies(ctx, true, []string{req.Name}, req.Resources)
// }

// func (o *PoliciesService) DetachPolicyFrom(ctx context.Context, req *v1.AttachPolicyRequest) (*emptypb.Empty, error) {
// 	return nil, keto.DefaultAccessMgr().AttachPolicies(ctx, false, []string{req.Name}, req.Resources)
// }

func (o *PoliciesService) GetAttachedPolicies(ctx context.Context, req *iam.GetAttachedPoliciesRequest) (
	*iam.GetAttachedPoliciesReply, error) {
	if req.Role != "" {
		p, err := keto.DefaultAccessMgr().GetResourcePolicyByRole(ctx, req.Name, req.Role)
		if err != nil {
			return nil, err
		}
		r := &iam.GetAttachedPoliciesReply{}
		if p != nil {
			r.PolicyNames = []string{p.Name}
		}
		return r, nil
	}

	names, err := keto.DefaultAccessMgr().GetAttachedPolicies(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	return &iam.GetAttachedPoliciesReply{PolicyNames: names}, nil
}

func (o *PoliciesService) CreateResource(ctx context.Context, req *iam.CreateResourceRequest) (
	*emptypb.Empty, error) {
	spew.Dump("---> create resource: ", req)
	// ensure the resource does not exist yet
	exist, err := keto.DefaultAccessMgr().IsObjectExist(ctx, req.Name)
	if exist {
		err = errors.NewErrConflict(errors.WithMessage("resource already exists"))
	}
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, keto.DefaultAccessMgr().CreateObjects(ctx, req.Owners, req.Parents, req.Name)
}

func (o *PoliciesService) DeleteResource(ctx context.Context, req *iam.DeleteResourceRequest) (
	*emptypb.Empty, error) {
	op := biz.UserFromCtx(ctx)
	if !biz.IsPrivilegedUser(op.User) && !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermSetPolicy, req.Name) {
		return nil, errors.NewErrForbidden()
	}
	return &emptypb.Empty{}, keto.DefaultAccessMgr().DeleteObject(ctx, req.Name)
}

func (o *PoliciesService) RevokeUsersRole(ctx context.Context, req *iam.RevokeUsersRoleRequest) (
	*emptypb.Empty, error) {
	op := biz.UserFromCtx(ctx)
	if !biz.IsPrivilegedUser(op.User) && !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermSetPolicy, req.Resource) {
		return nil, errors.NewErrForbidden()
	}
	return &emptypb.Empty{}, keto.DefaultAccessMgr().RevokeUsersRoleOnResource(ctx, req.Resource, req.Role, req.Users)
}

// func (o *PoliciesService) GetAttachedResources(ctx context.Context, req *v1.GetPolicyRequest) (
// 	*v1.GetAttachedResourcesReply, error) {
// 	p, err := keto.DefaultAccessMgr().GetPolicyAttachedResources(ctx, req.Name)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return &v1.GetAttachedResourcesReply{Resources: p}, nil
// }

// func (o *PoliciesService) ManageResourcePolicies(ctx context.Context, req *v1.ManageResourcePoliciesRequest) (
// 	*emptypb.Empty, error) {
// 	return nil, keto.DefaultAccessMgr().AttachPolicies(ctx, req.Action == v1.ManageResourcePoliciesRequest_attach,
// 		req.Policies, []string{req.Name})
// }

// func (o *PoliciesService) GetResourceTree(ctx context.Context, req *v1.GetResourceTreeRequest) (
// 	*v1.GetResourceTreeReply, error) {
// 	typ, id := keto.ParseResourceName(req.Name)
// 	rsp, err := keto.DefaultKeto().Expand(ctx, &keto.ExpandRequest{
// 		Subject:  keto.NewSubjectSet(typ, id, req.Relation),
// 		MaxDepth: req.MaxDepth,
// 	})
// 	if err != nil {
// 		return nil, err
// 	}

// 	return &v1.GetResourceTreeReply{Tree: copyResourceTree(rsp.Tree)}, nil
// }

// func copyResourceTree(src *keto.SubjectTree) *v1.GetResourceTreeReply_Node {
// 	if src == nil {
// 		return nil
// 	}

// 	getNodeName := func(node *keto.RelationTuple) string {
// 		r := &strings.Builder{}
// 		if node.Namespace != "" {
// 			r.WriteString(node.Namespace)
// 			r.WriteByte(':')
// 		}
// 		r.WriteString(node.Object)
// 		if node.Relation != "" {
// 			r.WriteByte('#')
// 			r.WriteString(node.Relation)
// 		}
// 		return r.String()
// 	}

// 	dst := &v1.GetResourceTreeReply_Node{Name: getNodeName(src.Tuple)}
// 	for _, p := range src.Children {
// 		if p.NodeType == keto.NodeTypeLeaf {
// 			dst.Items = append(dst.Items, getNodeName(p.Tuple))
// 			continue
// 		}
// 		// NodeTypes other than NodeTypeUnion are not considered at the time of writing
// 		dst.Inherits = append(dst.Inherits, copyResourceTree(p))
// 	}

// 	return dst
// }
