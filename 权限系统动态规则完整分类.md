# 权限系统动态规则完整分类

## 1. 动态规则分类概述

基于IAM项目中`iam/pkg/keto/`目录的实现，动态权限规则可以分为以下五大类：

1. **Role规则** (`role.go`) - 角色权限管理
2. **Policy规则** (`policy.go`) - 策略权限管理  
3. **User规则** (`user.go`) - 用户权限管理
4. **Group规则** (`group.go`) - 组织权限管理
5. **Object规则** (`object.go`) - 资源权限管理

## 2. Role规则 (角色权限管理)

### 2.1 动态规则模板

#### 角色基础模板
```go
// 所有角色都受系统管理员策略控制
roleTpl = "IamRole:<role-name>#policies@IamPolicy:sys/admin\n"

// 全局角色还受公共查看策略控制
roleViewerTpl = "IamRole:<role-name>#policies@IamPolicy:sys/public-view\n"

// 组织角色建立与父组织的关系
roleParentTpl = "IamRole:<role-name>#parents@IamGroup:<parent-name>\n"
```

### 2.2 对应静态规则

#### 全局角色权限继承
```
# 超级管理员角色继承链
IamRole:root#perms@IamRole:admin#perms
IamRole:superadmin#perms@IamRole:admin#perms
IamRole:service#perms@IamRole:admin#perms

# 管理员拥有所有子系统权限
IamRole:admin#perms@IamRole:iam.owner#perms
IamRole:admin#perms@IamRole:anno.owner#perms
IamRole:admin#perms@IamRole:anno.regulator#perms
```

#### 业务角色权限定义
```
# Anno业务角色组合
IamRole:anno.viewer#perms@IamRole:AnnoLot.viewer#perms
IamRole:anno.viewer#perms@IamRole:AnnoJob.viewer#perms
IamRole:anno.viewer#perms@IamRole:AnnoOrder.viewer#perms

IamRole:anno.editor#perms@IamRole:AnnoLot.editor#perms
IamRole:anno.editor#perms@IamRole:AnnoJob.editor#perms
IamRole:anno.editor#perms@IamRole:AnnoOrder.editor#perms
```

#### 资源级角色权限
```
# AnnoLot权限层级
IamRole:AnnoLot.viewer#<EMAIL>
IamRole:AnnoLot.viewer#<EMAIL>

IamRole:AnnoLot.editor#perms@IamRole:AnnoLot.viewer#perms
IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#<EMAIL>

IamRole:AnnoLot.owner#perms@IamRole:AnnoLot.editor#perms
IamRole:AnnoLot.owner#<EMAIL>
IamRole:AnnoLot.owner#<EMAIL>
IamRole:AnnoLot.owner#<EMAIL>
```

### 2.3 核心功能

- **CreateRole**: 创建角色并设置权限
- **GetRole**: 获取角色的直接权限
- **ExpandRolePerms**: 获取角色的所有权限（包括继承）
- **AddRolePerms**: 为角色添加权限
- **DeleteRolePerms**: 删除角色权限
- **DeleteRole**: 删除角色

## 3. Policy规则 (策略权限管理)

### 3.1 动态规则逻辑

#### 策略创建的三元组关系
```go
// 1. 策略绑定角色
NewTuple(PolicyNs, name, PolicyRelRoles, NewSubjectSet(RoleNs, role, ""))

// 2. 策略绑定用户
NewTuple(PolicyNs, name, PolicyRelUsers, NewSubjectSet(GroupNs, groupName, GroupRelMembers))

// 3. 资源绑定策略
NewTuple(resourceNs, resourceObj, RelPolicies, NewSubjectSet(PolicyNs, name, ""))
```

### 3.2 对应静态规则

#### 系统级策略
```
# 管理员策略
IamPolicy:sys/admin#roles@IamRole:admin
IamPolicy:sys/admin#users@IamGroup:sys/admin#members
IamPolicy:sys/admin#users@IamGroup:sys/service#members

# KAM策略
IamPolicy:sys/kam#roles@IamRole:kam
IamPolicy:sys/kam#users@IamGroup:sys/kam#members

# PM策略
IamPolicy:sys/pm#roles@IamRole:pm
IamPolicy:sys/pm#users@IamGroup:sys/pm#members
```

#### 全局隐式策略
```
# 所有角色都受管理员策略控制
IamRole:*#policies@IamPolicy:sys/admin

# 所有用户都受特定策略控制
IamUser:*#policies@IamPolicy:sys/admin
IamUser:*#policies@IamPolicy:sys/inspector

# 所有Anno资源都受管理员策略控制
AnnoProject:*#policies@IamPolicy:sys/admin
AnnoLot:*#policies@IamPolicy:sys/admin
AnnoJob:*#policies@IamPolicy:sys/admin
AnnoOrder:*#policies@IamPolicy:sys/admin
```

### 3.3 核心功能

- **CreatePolicy**: 创建策略并绑定角色、用户、资源
- **GetPolicy**: 获取策略的角色和用户绑定
- **EditPolicy**: 修改策略的用户绑定
- **DeletePolicy**: 删除策略
- **AttachPolicies**: 将策略附加到资源
- **GetAttachedPolicies**: 获取资源绑定的策略

## 4. User规则 (用户权限管理)

### 4.1 动态规则模板

#### 用户创建模板
```go
userTpl = `
# 用户所有者策略
IamPolicy:IamUser.<user-name>.owner#roles@IamRole:owner
IamPolicy:IamUser.<user-name>.owner#users@IamUser:<user-name>

# 用户自我所有权关系 (快速访问路径)
IamUser:<user-name>#owners@IamUser:<user-name>
IamUser:<user-name>#policies@IamPolicy:sys/admin

# 用户加入全局用户组
IamGroup:sys/allUsers#members@IamUser:<user-name>

# 用户资源空间策略
AnnoLot:IamUser.<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
AnnoOrder:IamUser.<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
AnnofeedData:IamUser.<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
AnnofeedFile:IamUser.<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
`
```

### 4.2 对应静态规则

#### 服务账号
```
# 系统服务账号
IamGroup:sys/service#members@IamUser:aaaaaaaanno
IamUser:aaaaaaaanno#policies@IamPolicy:sys/admin
```

#### 全局用户组 (预留，未启用)
```
#IamGroup:sys/allUsers#members@IamUser:xxx
#IamGroup:sys/admin#members@IamUser:xxx
#IamGroup:sys/root#members@IamUser:xxx
```

### 4.3 核心功能

- **CreateUsers**: 创建用户并设置基础权限
- **AllowE**: 检查用户是否有特定权限
- **makeCheckTuple**: 构造权限检查元组

## 5. Group规则 (组织权限管理)

### 5.1 动态规则模板

#### 组织基础模板
```go
groupTpl = `
# 组织所有者策略
IamPolicy:IamGroup.<group-name>.owner#roles@IamRole:owner
IamPolicy:IamGroup.<group-name>.owner#users@IamGroup:<group-name>.owner#members
IamGroup:<group-name>#policies@IamPolicy:IamGroup.<group-name>.owner
IamGroup:<group-name>#policies@IamPolicy:sys/admin

# 组织资源父级关系 (重要!)
AnnoLot:IamGroup.<group-name>#parents@IamGroup:<group-name>
AnnoOrder:IamGroup.<group-name>#parents@IamGroup:<group-name>
AnnofeedData:IamGroup.<group-name>#parents@IamGroup:<group-name>
AnnofeedFile:IamGroup.<group-name>#parents@IamGroup:<group-name>
`
```

#### 组织所有者模板
```go
groupOwnerTpl = `
IamGroup:<group-name>.owner#members@IamUser:<owner-user>
IamGroup:<group-name>#members@IamUser:<owner-user>
IamUser:<owner-user>#parents@IamGroup:<group-name>
`
```

#### 顶级组织模板
```go
orgTpl = `
# 顶级组织管理权限
IamRole:IamGroup.<group-name>#parents@IamGroup:<group-name>
IamUser:IamGroup.<group-name>#parents@IamGroup:<group-name>
IamGroup:IamGroup.<group-name>#parents@IamGroup:<group-name>
`
```

### 5.2 对应静态规则

#### 组织成员管理权限
```
# 成员管理权限
IamRole:IamGroup.editor#<EMAIL>
IamRole:IamGroup.editor#<EMAIL>
IamRole:IamGroup.editor#<EMAIL>

IamRole:IamGroup.owner#perms@IamRole:IamGroup.editor#perms
IamRole:IamGroup.owner#<EMAIL>
IamRole:IamGroup.owner#<EMAIL>
IamRole:IamGroup.owner#<EMAIL>
```

### 5.3 核心功能

- **CreateGroup**: 创建组织并设置权限
- **DeleteGroup**: 删除组织
- **AddGroupMembers**: 添加组织成员
- **DeleteGroupMembers**: 删除组织成员

## 6. Object规则 (资源权限管理)

### 6.1 动态规则模板

#### 资源基础模板
```go
resourceTpl = `
# 资源所有者策略
IamPolicy:<namespace>.<resource-name>.owner#roles@IamRole:<namespace>.owner
<namespace>:<resource-name>#policies@IamPolicy:<namespace>.<resource-name>.owner
<namespace>:<resource-name>#policies@IamPolicy:sys/admin
`
```

#### 资源所有者模板
```go
resourceOwnerUserTpl  = "IamPolicy:<namespace>.<resource-name>.owner#users@IamUser:<subject-name>\n"
resourceOwnerGroupTpl = "IamPolicy:<namespace>.<resource-name>.owner#users@IamGroup:<subject-name>#members\n"
```

#### 资源父级关系模板
```go
resourceParentTpl = "<namespace>:<resource-name>#parents@<parent>\n"
```

### 6.2 支持的资源类型

#### 当前支持的资源
- **AnnoLot**: 标注批次
- **AnnoJob**: 标注任务
- **AnnoOrder**: 标注订单
- **AnnofeedData**: 数据文件
- **AnnofeedFile**: 上传文件

#### 需要新增的资源 (Project层级)
- **AnnoProject**: 标注项目

### 6.3 核心功能

- **CreateObjects**: 创建资源并设置权限策略
- **DeleteObject**: 删除资源及其权限策略
- **CreateAccessPolicies**: 为资源创建访问策略

## 7. Project规则 (项目权限管理) - 新增

### 7.1 需要新增的动态规则模板

#### Project基础模板
```go
projectTpl = `
# 项目所有者策略
IamPolicy:AnnoProject.<project-name>.owner#roles@IamRole:AnnoProject.owner
AnnoProject:<project-name>#policies@IamPolicy:AnnoProject.<project-name>.owner
AnnoProject:<project-name>#policies@IamPolicy:sys/admin
`
```

#### Project所有者模板
```go
projectOwnerUserTpl  = "IamPolicy:AnnoProject.<project-name>.owner#users@IamUser:<subject-name>\n"
projectOwnerGroupTpl = "IamPolicy:AnnoProject.<project-name>.owner#users@IamGroup:<subject-name>#members\n"
```

#### Project父级关系模板
```go
projectParentTpl = "AnnoProject:<project-name>#parents@<parent>\n"
```

### 7.2 对应的静态规则 (需要添加到tuples.txt)

#### AnnoProject角色权限定义
```
# AnnoProject基础权限
IamRole:AnnoProject.viewer#<EMAIL>
IamRole:AnnoProject.viewer#<EMAIL>
IamRole:AnnoProject.viewer#<EMAIL>
IamRole:AnnoProject.viewer#<EMAIL>

IamRole:AnnoProject.editor#perms@IamRole:AnnoProject.viewer#perms
IamRole:AnnoProject.editor#<EMAIL>
IamRole:AnnoProject.editor#<EMAIL>
IamRole:AnnoProject.editor#<EMAIL>
IamRole:AnnoProject.editor#<EMAIL>

IamRole:AnnoProject.owner#perms@IamRole:AnnoProject.editor#perms
IamRole:AnnoProject.owner#<EMAIL>
IamRole:AnnoProject.owner#<EMAIL>
IamRole:AnnoProject.owner#<EMAIL>
```

#### Project权限继承到Lot
```
# 项目权限自动继承到Lot
IamRole:AnnoProject.owner#perms@IamRole:AnnoLot.owner#perms
IamRole:AnnoProject.editor#perms@IamRole:AnnoLot.editor#perms
IamRole:AnnoProject.viewer#perms@IamRole:AnnoLot.viewer#perms
```

#### 复合角色集成Project权限
```
# anno业务角色集成Project权限
IamRole:anno.owner#perms@IamRole:AnnoProject.owner#perms
IamRole:anno.editor#perms@IamRole:AnnoProject.editor#perms
IamRole:anno.viewer#perms@IamRole:AnnoProject.viewer#perms

# KAM和PM角色集成Project权限
IamRole:anno.kam#perms@IamRole:AnnoProject.owner#perms
IamRole:anno.pm#perms@IamRole:AnnoProject.editor#perms
```

#### 全局策略应用
```
# 所有AnnoProject受全局策略控制
AnnoProject:*#policies@IamPolicy:sys/admin
AnnoProject:*#policies@IamPolicy:sys/inspector
AnnoProject:*#policies@IamPolicy:sys/kam
AnnoProject:*#policies@IamPolicy:sys/pm
```

## 8. 权限规则生成流程

### 8.1 用户创建流程
```
1. CreateUsers(uid)
   ↓
2. 生成用户基础权限策略
   ↓
3. 建立用户自我所有权关系
   ↓
4. 加入全局用户组
   ↓
5. 创建用户资源空间策略
```

### 8.2 组织创建流程
```
1. CreateGroup(uid, owner, parent)
   ↓
2. 生成组织基础权限策略
   ↓
3. 设置组织所有者 (如果指定)
   ↓
4. 建立父级关系 (如果指定)
   ↓
5. 创建组织资源父级关系
```

### 8.3 资源创建流程
```
1. CreateObjects(objects, owners, parents)
   ↓
2. 为每个资源生成所有者策略
   ↓
3. 绑定资源所有者 (用户或组织)
   ↓
4. 建立资源父级关系
   ↓
5. 应用系统管理员策略
```

### 8.4 策略创建流程
```
1. CreatePolicy(resource, role, users)
   ↓
2. 创建策略并绑定角色
   ↓
3. 绑定策略适用用户
   ↓
4. 将策略附加到资源
```

## 9. 权限检查路径分析

### 9.1 直接权限检查
```
用户 → 策略 → 角色 → 权限
IamUser:alice → IamPolicy:xxx.owner → IamRole:xxx.owner → xxx.update
```

### 9.2 组织权限继承
```
用户 → 组织 → 组织策略 → 角色 → 权限
IamUser:alice → IamGroup:org123 → IamPolicy:org123.editor → IamRole:editor → xxx.create
```

### 9.3 资源权限继承
```
资源 → 父级资源 → 父级策略 → 角色 → 权限
AnnoJob:job123 → AnnoLot:lot456 → IamPolicy:lot456.owner → IamRole:AnnoLot.owner → AnnoJob.update
```

### 9.4 全局策略应用
```
任意资源 → 全局策略 → 管理员角色 → 所有权限
AnnoLot:* → IamPolicy:sys/admin → IamRole:admin → 所有权限
```

## 10. 动态规则与静态规则的关系

### 10.1 静态规则的作用
- **角色权限定义**: 定义每个角色拥有哪些权限
- **权限继承关系**: 定义角色之间的继承关系
- **全局策略**: 定义系统级的权限控制

### 10.2 动态规则的作用
- **实例化权限**: 为具体的用户、组织、资源创建权限实例
- **权限绑定**: 将用户与角色、资源进行绑定
- **策略管理**: 动态创建和管理权限策略

### 10.3 两者的协作
```
静态规则 (tuples.txt) + 动态规则 (iam/pkg/keto/) = 完整权限系统

静态规则定义"什么权限存在"
动态规则定义"谁拥有什么权限"
```

## 11. 权限规则命名规范

### 11.1 策略命名
- **系统策略**: `sys/<name>` (如: sys/admin, sys/kam)
- **资源策略**: `<namespace>.<resource-id>.<role>` (如: AnnoLot.lot123.owner)
- **用户策略**: `IamUser.<user-id>.owner`
- **组织策略**: `IamGroup.<group-id>.owner`

### 11.2 角色命名
- **全局角色**: `<role-name>` (如: admin, kam, pm)
- **业务角色**: `<business>.<role>` (如: anno.owner, iam.editor)
- **资源角色**: `<namespace>.<role>` (如: AnnoLot.owner, AnnoProject.editor)

### 11.3 资源命名
- **用户**: `IamUser:<user-id>`
- **组织**: `IamGroup:<group-id>`
- **业务资源**: `<Namespace>:<resource-id>` (如: AnnoLot:lot123)

## 12. 总结

### 12.1 动态规则分类总结
1. **Role规则**: 管理角色的创建、权限分配和继承
2. **Policy规则**: 管理策略的创建、绑定和应用
3. **User规则**: 管理用户的基础权限和资源空间
4. **Group规则**: 管理组织的权限和成员关系
5. **Object规则**: 管理资源的权限策略和父级关系

### 12.2 Project层级改造要点
- 在Object规则基础上新增Project规则
- 修改Lot的父级关系支持Project
- 添加Project相关的静态权限定义
- 实现双轨制兼容历史数据

### 12.3 设计优势
- **模块化**: 每类规则职责清晰，便于维护
- **可扩展**: 新增资源类型只需添加对应的Object规则
- **灵活性**: 支持复杂的权限继承和策略组合
- **一致性**: 所有规则遵循统一的命名和结构规范


### 动态权限规则分类架构图
```mermaid
graph TB
    subgraph "静态规则 (tuples.txt)"
        STATIC[角色权限定义<br/>权限继承关系<br/>全局策略]
    end
    
    subgraph "动态规则 (iam/pkg/keto/)"
        ROLE[Role规则<br/>role.go]
        POLICY[Policy规则<br/>policy.go]
        USER[User规则<br/>user.go]
        GROUP[Group规则<br/>group.go]
        OBJECT[Object规则<br/>object.go]
        PROJECT[Project规则<br/>project.go<br/><新增>]
    end
    
    subgraph "权限实体"
        USERS[用户实例]
        GROUPS[组织实例]
        ROLES_INST[角色实例]
        POLICIES_INST[策略实例]
        RESOURCES[资源实例]
    end
    
    subgraph "业务资源"
        ANNO_PROJECT[AnnoProject]
        ANNO_LOT[AnnoLot]
        ANNO_JOB[AnnoJob]
        ANNO_ORDER[AnnoOrder]
        ANNOFEED[AnnofeedData/File]
    end
    
    %% 静态规则到动态规则
    STATIC -.->|定义权限模板| ROLE
    STATIC -.->|定义策略模板| POLICY
    
    %% 动态规则到实体
    USER -->|创建管理| USERS
    GROUP -->|创建管理| GROUPS
    ROLE -->|创建管理| ROLES_INST
    POLICY -->|创建管理| POLICIES_INST
    OBJECT -->|创建管理| RESOURCES
    PROJECT -->|创建管理| ANNO_PROJECT
    
    %% 实体关系
    USERS -.->|成员关系| GROUPS
    POLICIES_INST -.->|绑定| USERS
    POLICIES_INST -.->|绑定| ROLES_INST
    RESOURCES -.->|绑定| POLICIES_INST
    
    %% 资源层级
    ANNO_PROJECT -->|父级| ANNO_LOT
    ANNO_LOT -->|父级| ANNO_JOB
    GROUPS -->|父级| ANNO_PROJECT
    GROUPS -->|父级| ANNO_LOT
    GROUPS -->|父级| ANNO_ORDER
    GROUPS -->|父级| ANNOFEED
    
    style STATIC fill:#e3f2fd
    style PROJECT fill:#fff3e0,stroke:#ff9800,stroke-width:3px
    style ROLE fill:#f3e5f5
    style POLICY fill:#e8f5e8
    style USER fill:#fce4ec
    style GROUP fill:#e0f2f1
    style OBJECT fill:#fff8e1
```

### 权限规则生成流程图
```mermaid
    sequenceDiagram
    participant U as 用户/系统
    participant IAM as IAM服务
    participant KETO as Keto引擎
    
    Note over U,KETO: 1. 用户创建流程
    U->>IAM: CreateUsers(uid)
    IAM->>IAM: 生成用户权限模板
    IAM->>KETO: 添加用户基础权限元组
    IAM->>KETO: 添加用户自我所有权元组
    IAM->>KETO: 添加全局用户组成员元组
    IAM->>KETO: 添加用户资源空间元组
    KETO-->>IAM: 权限规则创建完成
    
    Note over U,KETO: 2. 组织创建流程
    U->>IAM: CreateGroup(uid, owner, parent)
    IAM->>IAM: 生成组织权限模板
    IAM->>KETO: 添加组织基础权限元组
    alt 指定所有者
        IAM->>KETO: 添加组织所有者元组
    end
    alt 指定父级
        IAM->>KETO: 添加父级关系元组
    else 顶级组织
        IAM->>KETO: 添加顶级组织管理元组
    end
    IAM->>KETO: 添加组织资源父级元组
    KETO-->>IAM: 组织权限创建完成
    
    Note over U,KETO: 3. 资源创建流程
    U->>IAM: CreateObjects(objects, owners, parents)
    IAM->>IAM: 生成资源权限模板
    loop 每个资源
        IAM->>KETO: 添加资源所有者策略元组
        IAM->>KETO: 添加资源所有者绑定元组
        IAM->>KETO: 添加资源父级关系元组
        IAM->>KETO: 添加系统管理员策略元组
    end
    KETO-->>IAM: 资源权限创建完成
    
    Note over U,KETO: 4. 策略创建流程
    U->>IAM: CreatePolicy(resource, role, users)
    IAM->>IAM: 生成策略名称
    IAM->>KETO: 添加策略绑定角色元组
    IAM->>KETO: 添加策略绑定用户元组
    IAM->>KETO: 添加资源绑定策略元组
    KETO-->>IAM: 策略创建完成
    
    Note over U,KETO: 5. Project创建流程 (新增)
    U->>IAM: CreateProject(uid, owner, parent)
    IAM->>IAM: 生成Project权限模板
    IAM->>KETO: 添加Project所有者策略元组
    IAM->>KETO: 添加Project所有者绑定元组
    IAM->>KETO: 添加Project父级关系元组
    IAM->>KETO: 添加系统管理员策略元组
    KETO-->>IAM: Project权限创建完成
```