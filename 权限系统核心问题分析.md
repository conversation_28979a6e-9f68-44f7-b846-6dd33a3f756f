# 权限系统核心问题分析

## 1. Policy模型的本质

### 1.1 Policy是自定义的，不是Keto默认的

**答案：Policy是项目自定义的模型，不是Keto默认的！**

从`namespaces.keto.ts`文件可以看出：

```typescript
// 这是项目自定义的Policy模型
class IamPolicy implements Namespace {
  related: {
    roles: IamRole[] // 策略绑定的角色
    users: (IamUser | SubjectSet<IamGroup, "members">)[] // 策略适用的用户
  }

  permits = {
    // 自定义的权限检查逻辑
    check: (ctx: Context, perm: string): boolean =>
      this.related.users.includes(ctx.subject) &&
      this.related.roles.traverse((p) => p.permits.has_perm(ctx, perm)),
  }
}
```

### 1.2 Keto支持各种权限模型

**Keto是一个通用的权限引擎，支持多种权限模型：**

- **RBAC (Role-Based Access Control)**: 基于角色的访问控制
- **ABAC (Attribute-Based Access Control)**: 基于属性的访问控制  
- **ReBAC (Relationship-Based Access Control)**: 基于关系的访问控制
- **自定义模型**: 可以根据业务需求定义任意权限模型

### 1.3 项目的ReBAC实现

**是的，项目的ReBAC是自行实现的！**

项目通过以下方式实现了ReBAC：

1. **自定义命名空间**: 定义了IamUser、IamGroup、IamRole、IamPolicy等实体
2. **自定义关系**: 定义了members、perms、policies、parents等关系
3. **自定义权限检查逻辑**: 在每个命名空间中实现了permits方法
4. **策略模式**: 通过IamPolicy实现了用户-角色-权限的三元绑定

## 2. Job权限创建分析

### 2.1 Job创建的权限策略

**Job确实有权限策略创建！**

在`anno/internal/biz/job.go`中的Create方法：

```go
func (o *JobsBiz) Create(ctx context.Context, p *Job) (job *Job, err error) {
    // ...
    err = o.Repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        job, err = o.Repo.Create(ctx, p)
        if err != nil {
            return err
        }

        // Job权限策略创建！
        return client.CreateAccessPolicies(ctx, PermClsJob, p.GetUid(), nil,
            []string{LotScope(kid.StringID(job.LotID))})
    })
}
```

### 2.2 Job权限策略的特点

**Job的权限策略有以下特点：**

1. **没有直接所有者**: `owners`参数为`nil`，Job没有直接的个人所有者
2. **继承Lot权限**: `parents`设置为对应的Lot，Job继承Lot的所有权限
3. **自动创建**: 在workflow中创建Job时会自动调用权限策略创建

### 2.3 Workflow中的Job创建

在`anno/workflow/lotwf/lotstart.go`的`createJob`方法中：

```go
func (o *Activities) createJob(ctx context.Context, lot *biz.Lot, elems []*anno.Element, jobIdx int) error {
    // 1. 创建Job实体
    job := &biz.Job{
        ID:       newID,
        LotID:    lot.ID,
        // ...
    }
    
    // 2. 调用JobsBiz.Create，会自动创建权限策略
    _, err = o.jobbiz.Create(ctx, job)
    return err
}
```

**所以Job的权限策略确实会在workflow中自动创建！**

## 3. 普通用户权限分析

### 3.1 组织成员的默认权限

**普通成员默认没有Job权限，需要通过以下路径获得权限：**

#### 路径1：通过Lot权限继承
```
用户A → 组织org123成员 → 组织策略 → 角色权限 → Lot权限 → Job权限(继承)
```

#### 路径2：通过执行团队权限
```
用户A → 执行团队成员 → 团队策略 → JobViewer角色 → Job查看权限
```

### 3.2 权限继承机制

**Job权限通过以下机制继承：**

1. **Job继承Lot权限**: 
   ```
   AnnoJob:job123#parents@AnnoLot:lot456
   ```

2. **Lot权限检查逻辑**:
   ```typescript
   class AnnoLot implements Namespace {
     permits = {
       check: (ctx: Context, perm: string): boolean =>
         AnnoLot:"*".permits.check(ctx, perm) ||
         this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
         this.related.policies.traverse((p) => p.permits.check(ctx, perm)),
     }
   }
   ```

3. **Job权限检查逻辑**:
   ```typescript
   class AnnoJob implements Namespace {
     permits = {
       check: (ctx: Context, perm: string): boolean =>
         AnnoJob:"*".permits.check(ctx, perm) ||
         this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
         this.related.policies.traverse((p) => p.permits.check(ctx, perm)),
     }
   }
   ```

### 3.3 执行团队权限机制

**Lot创建时会为执行团队授予Job查看权限：**

在`anno/internal/biz/lot.go`中：

```go
func (o *LotsBiz) Create(ctx context.Context, p *Lot) (lot *Lot, err error) {
    // ...
    // 创建Lot基础权限
    err = client.CreateAccessPolicies(ctx, PermClsLot, p.GetUid(),
        []string{client.UserScope(p.CreatorUid)}, []string{client.GroupScope(p.OrgUid)})
    
    // 为执行团队授予权限
    err = o.grantLotExecteamAccess(ctx, lot)
}

func (o *LotsBiz) GrantExecteamAccess(ctx context.Context, lotID int64, teamUids ...string) (err error) {
    resource := LotScope(kid.StringID(lotID))
    users := lo.Map(teamUids, func(v string, _ int) string { return client.GroupScope(v) })
    
    // 为执行团队创建JobViewer权限策略
    _, err = client.CreatePolicy(ctx, resource, RoleJobViewer, users)
    return err
}
```

## 4. 用户A的权限场景分析

### 4.1 场景描述
- 用户A属于org123的成员
- org123的owner创建了lot
- 用户A能否看到lot列表和job？

### 4.2 权限检查路径

#### 查看Lot列表
```
1. 用户A请求查看Lot列表
2. 检查AnnoLot.list权限
3. 权限检查路径：
   IamUser:alice → IamGroup:org123#members → IamGroup:org123#policies → 
   IamPolicy:org123.xxx → IamRole:xxx → AnnoLot.list权限
```

#### 查看Job列表  
```
1. 用户A请求查看Job列表
2. 检查AnnoJob.list权限
3. 权限检查路径：
   AnnoJob:job123 → AnnoLot:lot456(父级) → 继承Lot的权限检查逻辑
```

### 4.3 关键因素

**用户A能否看到取决于：**

1. **组织权限策略**: org123是否有相应的权限策略
2. **角色权限定义**: 策略绑定的角色是否有list权限
3. **执行团队设置**: 用户A是否属于Lot的执行团队
4. **全局策略**: 是否有全局策略覆盖

### 4.4 典型情况

**在典型配置下：**

- ✅ **可以看到Lot列表**: 如果组织有viewer或更高权限的策略
- ✅ **可以看到Job列表**: Job继承Lot权限，如果能看Lot就能看Job
- ❌ **不能编辑**: 普通成员通常只有查看权限，没有编辑权限

## 5. 总结

### 5.1 核心发现

1. **Policy是自定义模型**: 项目基于Keto实现了自己的ReBAC权限模型
2. **Job有权限策略**: Job在创建时会自动生成权限策略，继承Lot权限
3. **权限继承完整**: Job → Lot → 组织的三层权限继承机制完善
4. **执行团队机制**: 通过执行团队可以为特定用户组授予Job权限

### 5.2 权限设计优势

- **灵活性**: 支持复杂的权限继承和策略组合
- **可扩展性**: 可以轻松添加新的权限模型和关系
- **细粒度控制**: 支持资源级别的精确权限控制
- **业务适配**: 很好地适配了标注业务的权限需求

#### 用户A查看Job权限检查流程
```mermaid
sequenceDiagram
    participant A as 用户A
    participant FE as 前端
    participant Anno as Anno服务
    participant IAM as IAM服务
    participant Keto as Keto引擎
    
    Note over A,Keto: 用户A查看Job列表的权限检查流程
    
    A->>FE: 请求查看Job列表
    FE->>Anno: GetJobList(ctx, req)
    
    Anno->>Anno: 获取用户A信息
    Note over Anno: user = biz.UserFromCtx(ctx)
    
    Anno->>IAM: IsAllowed(ctx, "", "AnnoJob.list", "AnnoJob:*")
    Note over IAM: 检查用户A是否有Job列表权限
    
    IAM->>Keto: Check权限元组
    Note over Keto: 构造检查元组:<br/>AnnoJob:*#list@IamUser:alice
    
    Keto->>Keto: 执行权限检查逻辑
    Note over Keto: AnnoJob命名空间的permits.check方法
    
    alt 检查全局Job权限
        Keto->>Keto: AnnoJob:"*".permits.check(ctx, "AnnoJob.list")
        Note over Keto: 检查是否有全局Job权限
    else 检查父级Lot权限
        Keto->>Keto: this.related.parents.traverse((p) => p.permits.check(ctx, "AnnoJob.list"))
        Note over Keto: Job继承Lot权限检查
        
        Keto->>Keto: AnnoLot权限检查
        Note over Keto: 检查用户A对Lot的权限
        
        alt 检查组织权限
            Keto->>Keto: this.related.parents.traverse((p) => p.permits.check(ctx, "AnnoJob.list"))
            Note over Keto: Lot继承组织权限
            
            Keto->>Keto: IamGroup:org123权限检查
            Note over Keto: 检查用户A在org123的权限
            
            alt 检查组织策略
                Keto->>Keto: this.related.policies.traverse((p) => p.permits.check(ctx, "AnnoJob.list"))
                Note over Keto: 检查组织绑定的策略
                
                Keto->>Keto: IamPolicy策略检查
                Note over Keto: 检查策略是否适用于用户A<br/>且绑定的角色有相应权限
                
                alt 用户匹配且角色有权限
                    Keto-->>IAM: 返回 true (允许)
                else 用户不匹配或角色无权限
                    Keto-->>IAM: 返回 false (拒绝)
                end
            else 无组织策略
                Keto-->>IAM: 返回 false (拒绝)
            end
        else 无组织权限
            Keto-->>IAM: 返回 false (拒绝)
        end
    else 检查Job直接策略
        Keto->>Keto: this.related.policies.traverse((p) => p.permits.check(ctx, "AnnoJob.list"))
        Note over Keto: 检查Job直接绑定的策略<br/>(通常为空，Job继承Lot权限)
    end
    
    IAM-->>Anno: 返回权限检查结果
    
    alt 权限允许
        Anno->>Anno: 查询Job列表数据
        Anno-->>FE: 返回Job列表
        FE-->>A: 显示Job列表
    else 权限拒绝
        Anno-->>FE: 返回权限错误
        FE-->>A: 显示"权限不足"
    end
```
