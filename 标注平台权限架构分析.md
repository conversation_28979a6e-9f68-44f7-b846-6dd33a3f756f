# 标注平台权限架构分析

## 1. 整体架构概述

标注平台采用基于 **Ory Keto** 的关系元组权限系统，这是一个现代化的细粒度权限控制系统，类似于 Google Zanzibar 的开源实现。整个权限架构由三个核心项目组成：

- **Anno项目**：标注业务服务，负责创建lot、job等业务资源时调用权限服务
- **IAM项目**：身份认证与权限管理服务，作为权限系统的中间层
- **Keto项目**：底层权限引擎，基于关系元组实现权限检查

## 2. 权限模型分析

### 2.1 权限模型类型
该系统采用的是 **ReBAC (Relationship-Based Access Control)** 模型，而不是传统的RBAC。ReBAC比RBAC更加灵活，能够表达复杂的权限关系：

- **RBAC**：用户 → 角色 → 权限
- **ReBAC**：主体 → 关系 → 客体，支持继承、委托等复杂关系

### 2.2 核心概念

#### 关系元组 (Relation Tuples)
权限系统的基础是关系元组，格式为：`namespace:object#relation@subject`

例如：
```
IamGroup:sys/admin#members@IamUser:alice
AnnoLot:lot123#policies@IamPolicy:lot123.owner
```

#### 命名空间 (Namespaces)
系统定义了多个命名空间，每个代表不同的资源类型：

- **IamUser**: 用户
- **IamGroup**: 组织/团队  
- **IamRole**: 角色
- **IamPolicy**: 策略
- **AnnoLot**: 标注批次
- **AnnoJob**: 标注任务
- **AnnoOrder**: 标注订单
- **AnnoProject**: 标注项目

## 3. 权限检查流程

### 3.1 业务层权限检查
以创建Lot为例，权限检查流程：

```go
// anno/internal/service/lot.go
func (o *LotsService) CreateLot(ctx context.Context, req *anno.CreateLotRequest) (*anno.Lot, error) {
    // 1. 获取用户信息
    user := biz.UserFromCtx(ctx)
    scope := client.GroupScope(req.OrgUid)
    
    // 2. 调用权限检查
    if !client.IsAllowed(ctx, "", biz.PermCreate, biz.PermClsLot, scope) {
        return nil, errors.NewErrForbidden()
    }
    
    // 3. 创建业务资源
    lot, err := o.bz.Create(ctx, lot)
    return FromBizLot(lot), err
}
```

### 3.2 IAM层权限处理
```go
// apis/client/iam.go
func IsAllowed(ctx context.Context, uid, perm, objCls, object string) bool {
    // 构造权限检查请求
    rp, err := iamsvc.IsAllowed(ctx, &iamv1.IsAllowedRequest{
        Uid: uid,
        Action: &iamv1.IsAllowedRequest_Action{
            Resource: objCls + ":" + object,  // 如: "AnnoLot:org123"
            Perm:     objCls + "." + perm,    // 如: "AnnoLot.create"
        }})
    return err == nil && rp.Allowed
}
```

### 3.3 Keto层权限验证
```go
// iam/pkg/keto/user.go
func (o *AccessMgr) AllowE(ctx context.Context, user, perm, obj string) (allowed bool, err error) {
    // 1. 构造关系元组
    t, err := o.makeCheckTuple(user, perm, obj)
    
    // 2. 调用Keto引擎检查
    return o.kt.Check(ctx, &CheckRequest{Tuple: t})
}
```

## 4. 权限规则定义

### 4.1 静态规则 (tuples.txt)
定义了系统的基础权限规则，包括：

#### 全局角色权限
```
# 管理员角色拥有所有Anno相关权限
IamRole:admin#perms@IamRole:anno.owner#perms
IamRole:admin#perms@IamRole:anno.regulator#perms

# KAM角色权限
IamRole:anno.kam#perms@IamRole:AnnoLot.owner#perms
IamRole:anno.kam#perms@IamRole:AnnoJob.owner#perms
IamRole:anno.kam#perms@IamRole:AnnoOrder.owner#perms
```

#### 资源特定权限
```
# AnnoLot权限定义
IamRole:AnnoLot.viewer#<EMAIL>
IamRole:AnnoLot.viewer#<EMAIL>
IamRole:AnnoLot.editor#perms@IamRole:AnnoLot.viewer#perms
IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#<EMAIL>
```

#### 全局隐式策略
```
# 所有资源都受管理员策略控制
AnnoLot:*#policies@IamPolicy:sys/admin
AnnoJob:*#policies@IamPolicy:sys/admin
IamRole:*#policies@IamPolicy:sys/admin
```

### 4.2 动态规则 (namespaces.keto.ts)
定义了权限检查的逻辑规则：

```typescript
class AnnoLot implements Namespace {
  related: {
    parents: (IamGroup|AnnoLot|AnnoProject)[]
    policies: IamPolicy[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoLot:"*".permits.check(ctx, perm) ||  // 全局权限
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||  // 父级权限继承
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),   // 策略权限

    create: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.create"),
    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.get"),
    // ... 其他权限方法
  }
}
```

## 5. 策略 (Policy) 的作用

### 5.1 策略的定义
策略是连接用户/组织和角色的桥梁，定义了"谁"可以拥有"什么角色"：

```typescript
class IamPolicy implements Namespace {
  related: {
    roles: IamRole[]  // 策略绑定的角色
    users: (IamUser | SubjectSet<IamGroup, "members">)[]  // 策略适用的用户
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      this.related.users.includes(ctx.subject) &&  // 用户在策略范围内
      this.related.roles.traverse((p) => p.permits.has_perm(ctx, perm)),  // 角色有对应权限
  }
}
```

### 5.2 策略的工作机制
1. **用户匹配**：检查当前用户是否在策略的用户列表中
2. **角色权限**：检查策略绑定的角色是否有所需权限
3. **双重验证**：只有同时满足用户匹配和角色权限才允许访问

### 5.3 策略示例
```
# 系统管理员策略
IamPolicy:sys/admin#roles@IamRole:admin
IamPolicy:sys/admin#users@IamGroup:sys/admin#members

# 特定资源策略
IamPolicy:AnnoLot:lot123.owner#roles@IamRole:AnnoLot.owner
IamPolicy:AnnoLot:lot123.owner#users@IamUser:alice
```

## 6. 权限继承机制

系统支持多层级的权限继承：

### 6.1 组织层级继承
```
AnnoProject → IamGroup (组织)
AnnoLot → AnnoProject (项目) 
AnnoJob → AnnoLot (批次)
```

### 6.2 角色层级继承
```
anno.kam → anno.regulator → AnnoLot.executor
anno.owner → AnnoLot.owner → AnnoLot.editor → AnnoLot.viewer
```

### 6.3 全局权限
通过 `*` 通配符实现全局权限：
```
AnnoLot:*#policies@IamPolicy:sys/admin  // 所有AnnoLot都受admin策略控制
```

## 7. 权限创建和管理

### 7.1 资源创建时的权限设置
当创建新的业务资源时，系统会自动创建相应的权限策略：

```go
// anno/internal/biz/lot.go - 创建Lot时
func (o *LotsBiz) Create(ctx context.Context, p *Lot) (lot *Lot, err error) {
    err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        // 1. 创建业务资源
        lot, err = o.repo.Create(ctx, p)

        // 2. 创建权限策略
        err = client.CreateAccessPolicies(ctx, PermClsLot, p.GetUid(),
            []string{client.UserScope(p.CreatorUid)},     // 创建者作为owner
            []string{client.GroupScope(p.OrgUid)})        // 所属组织作为parent

        return err
    })
}
```

### 7.2 权限策略的动态创建
```go
// iam/pkg/keto/policy.go
func (o *AccessMgr) CreatePolicy(ctx context.Context, resource, role string, users []string) (name string, err error) {
    // 1. 解析资源名称
    rns, robj := ParseObjectName(resource)
    name = EscapeName(resource) + "." + role

    // 2. 创建策略用户关系
    tps, err := o.makePolicyUsersTuples(name, users)

    // 3. 绑定策略到角色
    tps = append(tps, NewTuple(PolicyNs, name, PolicyRelRoles, NewSubjectSet(RoleNs, role, "")))

    // 4. 绑定策略到资源
    tps = append(tps, NewTuple(rns, robj, RelPolicies, NewSubjectSet(PolicyNs, name, "")))

    return name, o.kt.Add(ctx, tps...)
}
```

## 8. 实际应用场景分析

### 8.1 创建Lot的完整权限流程

1. **前端请求**：用户在前端点击"创建批次"
2. **服务层检查**：Anno服务检查用户是否有 `AnnoLot.create` 权限
3. **IAM层转发**：IAM服务将权限检查请求转发给Keto
4. **Keto引擎计算**：
   - 检查用户是否在相关策略中
   - 检查策略绑定的角色是否有create权限
   - 检查父级组织是否有相应权限
   - 检查全局策略是否适用
5. **权限决策**：返回允许/拒绝结果
6. **资源创建**：如果允许，创建Lot并设置其权限策略

### 8.2 权限检查的多层验证

以用户Alice创建Lot为例：

```
用户: IamUser:alice
组织: IamGroup:org123
权限: AnnoLot.create
资源: AnnoLot:org123
```

Keto会检查以下路径：
1. **直接权限**：alice是否直接有AnnoLot.create权限
2. **角色权限**：alice的角色是否有AnnoLot.create权限
3. **组织权限**：org123是否允许其成员创建Lot
4. **策略权限**：相关策略是否授予alice创建权限
5. **全局权限**：全局策略是否适用

## 9. 权限系统的优势

### 9.1 细粒度控制
- 可以精确控制到具体资源的具体操作
- 支持资源级别的权限隔离
- 灵活的权限组合和继承

### 9.2 可扩展性
- 新增资源类型只需定义新的namespace
- 权限规则可以动态添加和修改
- 支持复杂的业务权限逻辑

### 9.3 性能优化
- 关系元组的高效存储和查询
- 权限检查结果可以缓存
- 支持批量权限检查

## 10. 潜在的改进方向

### 10.1 权限管理界面
当前系统缺少直观的权限管理界面，建议：
- 开发权限可视化管理工具
- 提供权限关系图展示
- 支持权限模板和批量操作

### 10.2 审计和监控
- 增加权限检查日志记录
- 提供权限变更审计功能
- 监控权限系统性能指标

### 10.3 多组织支持优化
针对"多个组织负责同一环节"的需求：
- 优化团队权限隔离设计
- 支持跨组织的权限委托
- 实现更灵活的权限共享机制

## 11. 总结

标注平台采用的ReBAC权限模型是一个现代化、灵活且强大的权限系统。通过关系元组和策略的组合，能够满足复杂的业务权限需求。系统的三层架构（Anno-IAM-Keto）提供了良好的解耦和可维护性，为后续的权限功能扩展奠定了坚实的基础。
