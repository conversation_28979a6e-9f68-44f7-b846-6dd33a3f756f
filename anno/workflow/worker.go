package workflow

import (
	"anno/internal/biz"
	"anno/internal/conf"
	"anno/workflow/common"
	"anno/workflow/jobwf"
	"anno/workflow/lotwf"
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"

	"github.com/google/wire"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/wf"
)

var ProviderSet = wire.NewSet(lotwf.NewActivities, jobwf.NewActivities, NewWorkflowStarter)

func Init(c *conf.Temporal, logger log.Logger) {
	wf.Init(&wf.Config{
		Logger:    logger,
		Addr:      c.Addr,
		Taskq:     c.TaskQueue,
		Namespace: c.Namespace,
	})
}

func StartWorker(lact *lotwf.Activities, jact *jobwf.Activities) {
	wf.RegisterActivity(lact, jact)
	wf.RegisterWorkflow(jobwf.JobWorkflow, lotwf.LotWorkflow, lotwf.LotStartWorkflow)
	wf.Start()
}

type workflowStarter struct {
	log *log.Helper
}

func NewWorkflowStarter(logger log.Logger) biz.BackgroundTask {
	return &workflowStarter{
		log: log.NewHelper(logger),
	}
}

// func (o *workflowStarter) StartLot(ctx context.Context, lotID int64) error {
// 	wfid := lot.GetLotStartWfID(lotID)
// 	args := &wf.WfArgsOpts{
// 		Workflow:     lot.LotStartWorkflow,
// 		WorkflowArgs: []any{lotID},
// 		Options: wf.StartWorkflowOptions{
// 			RetryPolicy: util.RetryPolicy(100),
// 		},
// 	}
// 	_, err := wf.StartWorkflow(ctx, wfid, args)
// 	return err
// }

func (o *workflowStarter) SignalEvent(ctx context.Context, ev *common.Event) error {
	spew.Dump("---> bg signal event: ", ev)
	ev.TraceID = log.GetTraceID(ctx)
	switch {
	case ev.JobID != 0:
		return jobwf.SignalJobWf(ctx, ev.JobID, ev)
	case ev.LotID != 0:
		return lotwf.SignalLotWf(ctx, ev.LotID, ev)
	default:
		return fmt.Errorf("cannot decide workflow type")
	}
}
