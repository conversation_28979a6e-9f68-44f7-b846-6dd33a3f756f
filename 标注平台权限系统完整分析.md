# 标注平台权限系统完整分析

## 1. 权限系统架构总览

### 1.1 静态规则 vs 动态规则
- **静态规则** (`keto/tuples.txt`): 预定义的权限规则，系统启动时加载
- **动态规则** (`keto/namespaces.keto.ts` + `iam/pkg/keto/*.go`): 运行时生成的权限规则

### 1.2 权限定义的三个层次
1. **命名空间定义** (`namespaces.keto.ts`): 定义权限检查逻辑
2. **静态权限规则** (`tuples.txt`): 预设的角色、权限关系
3. **动态权限管理** (`iam/pkg/keto/`): 运行时权限操作

## 2. 静态权限规则分析 (tuples.txt)

### 2.1 用户相关规则

#### 服务账号
```
# 系统服务账号
IamGroup:sys/service#members@IamUser:aaaaaaaanno
IamUser:aaaaaaaanno#policies@IamPolicy:sys/admin
```

#### 全局用户组 (已注释，未启用)
```
#IamGroup:sys/allUsers#members@IamUser:xxx
#IamGroup:sys/admin#members@IamUser:xxx
#IamGroup:sys/root#members@IamUser:xxx
#IamGroup:sys/inspector#members@IamUser:xxx
#IamGroup:sys/kam#members@IamUser:xxx
#IamGroup:sys/pm#members@IamUser:xxx
```

### 2.2 角色层级体系

#### 全局管理角色
```
# 超级管理员角色继承链
IamRole:root#perms@IamRole:admin#perms
IamRole:superadmin#perms@IamRole:admin#perms
IamRole:service#perms@IamRole:admin#perms

# 管理员拥有所有子系统权限
IamRole:admin#perms@IamRole:iam.owner#perms
IamRole:admin#perms@IamRole:anno.owner#perms
IamRole:admin#perms@IamRole:anno.regulator#perms
IamRole:admin#perms@IamRole:annofeed.owner#perms
IamRole:admin#perms@IamRole:fe.owner#perms
```

#### 业务角色层级
```
# KAM (Key Account Manager) 角色
IamRole:kam#perms@IamRole:iam.editor#perms
IamRole:kam#perms@IamRole:anno.kam#perms

# PM (Project Manager) 角色  
IamRole:pm#perms@IamRole:iam.viewer#perms
IamRole:pm#<EMAIL>

# 查看者角色
IamRole:viewer#perms@IamRole:iam.viewer#perms
IamRole:viewer#perms@IamRole:anno.viewer#perms
IamRole:viewer#perms@IamRole:annofeed.viewer#perms
IamRole:viewer#perms@IamRole:fe.viewer#perms
```

### 2.3 Anno业务权限规则

#### AnnoLot (标注批次) 权限
```
# 查看者权限
IamRole:AnnoLot.viewer#<EMAIL>
IamRole:AnnoLot.viewer#<EMAIL>

# 编辑者权限 (继承查看者)
IamRole:AnnoLot.editor#perms@IamRole:AnnoLot.viewer#perms
IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#<EMAIL>

# 所有者权限 (继承编辑者)
IamRole:AnnoLot.owner#perms@IamRole:AnnoLot.editor#perms
IamRole:AnnoLot.owner#<EMAIL>
IamRole:AnnoLot.owner#<EMAIL>
IamRole:AnnoLot.owner#<EMAIL>

# 执行者权限 (特殊权限)
IamRole:AnnoLot.executor#<EMAIL>
```

#### AnnoJob (标注任务) 权限
```
# 查看者权限
IamRole:AnnoJob.viewer#<EMAIL>  # 可以查看父级Lot
IamRole:AnnoJob.viewer#<EMAIL>
IamRole:AnnoJob.viewer#<EMAIL>
IamRole:AnnoJob.viewer#<EMAIL>

# 编辑者权限
IamRole:AnnoJob.editor#perms@IamRole:AnnoJob.viewer#perms
IamRole:AnnoJob.editor#<EMAIL>
IamRole:AnnoJob.editor#<EMAIL>
IamRole:AnnoJob.editor#<EMAIL>

# 所有者权限
IamRole:AnnoJob.owner#perms@IamRole:AnnoJob.editor#perms
IamRole:AnnoJob.owner#<EMAIL>
IamRole:AnnoJob.owner#<EMAIL>
IamRole:AnnoJob.owner#<EMAIL>

# 监管者权限
IamRole:AnnoJob.regulator#<EMAIL>
IamRole:AnnoJob.regulator#<EMAIL>
IamRole:AnnoJob.regulator#<EMAIL>
```

#### AnnoOrder (标注订单) 权限
```
# 查看者权限
IamRole:AnnoOrder.viewer#<EMAIL>
IamRole:AnnoOrder.viewer#<EMAIL>

# 编辑者权限
IamRole:AnnoOrder.editor#perms@IamRole:AnnoOrder.viewer#perms
IamRole:AnnoOrder.editor#<EMAIL>
IamRole:AnnoOrder.editor#<EMAIL>
IamRole:AnnoOrder.editor#<EMAIL>
IamRole:AnnoOrder.editor#<EMAIL>

# 所有者权限
IamRole:AnnoOrder.owner#perms@IamRole:AnnoOrder.editor#perms
IamRole:AnnoOrder.owner#<EMAIL>
IamRole:AnnoOrder.owner#<EMAIL>
IamRole:AnnoOrder.owner#<EMAIL>
```

### 2.4 复合业务角色

#### Anno业务角色组合
```
# anno.viewer - 标注查看者
IamRole:anno.viewer#perms@IamRole:AnnoLot.viewer#perms
IamRole:anno.viewer#perms@IamRole:AnnoJob.viewer#perms
IamRole:anno.viewer#perms@IamRole:AnnoOrder.viewer#perms

# anno.editor - 标注编辑者
IamRole:anno.editor#perms@IamRole:AnnoLot.editor#perms
IamRole:anno.editor#perms@IamRole:AnnoJob.editor#perms
IamRole:anno.editor#perms@IamRole:AnnoOrder.editor#perms

# anno.owner - 标注所有者
IamRole:anno.owner#perms@IamRole:AnnoLot.owner#perms
IamRole:anno.owner#perms@IamRole:AnnoJob.owner#perms
IamRole:anno.owner#perms@IamRole:AnnoOrder.owner#perms

# anno.regulator - 标注监管者
IamRole:anno.regulator#perms@IamRole:AnnoLot.executor#perms
IamRole:anno.regulator#perms@IamRole:AnnoJob.regulator#perms

# anno.kam - KAM角色 (最高业务权限)
IamRole:anno.kam#perms@IamRole:anno.regulator#perms
IamRole:anno.kam#perms@IamRole:AnnoLot.owner#perms
IamRole:anno.kam#perms@IamRole:AnnoJob.owner#perms
IamRole:anno.kam#perms@IamRole:AnnoOrder.owner#perms
IamRole:anno.kam#perms@IamRole:AnnofeedData.owner#perms
IamRole:anno.kam#perms@IamRole:AnnofeedFile.owner#perms

# anno.pm - PM角色
IamRole:anno.pm#perms@IamRole:anno.regulator#perms
IamRole:anno.pm#perms@IamRole:AnnoLot.editor#perms
IamRole:anno.pm#perms@IamRole:AnnoJob.editor#perms
```

### 2.5 策略定义

#### 系统级策略
```
# 管理员策略
IamPolicy:sys/admin#roles@IamRole:admin
IamPolicy:sys/admin#users@IamGroup:sys/admin#members
IamPolicy:sys/admin#users@IamGroup:sys/service#members
IamPolicy:sys/admin#users@IamGroup:sys/root#members
IamPolicy:sys/admin#users@IamGroup:sys/superadmin#members

# 检查员策略
IamPolicy:sys/inspector#roles@IamRole:inspector
IamPolicy:sys/inspector#users@IamGroup:sys/inspector#members

# 公共查看策略
IamPolicy:sys/publicView#roles@IamRole:viewer
IamPolicy:sys/publicView#users@IamGroup:sys/allUsers#members

# KAM策略
IamPolicy:sys/kam#roles@IamRole:kam
IamPolicy:sys/kam#users@IamGroup:sys/kam#members

# PM策略
IamPolicy:sys/pm#roles@IamRole:pm
IamPolicy:sys/pm#users@IamGroup:sys/pm#members
```

### 2.6 全局隐式策略
```
# 所有角色都受管理员策略控制
IamRole:*#policies@IamPolicy:sys/admin
IamRole:*#policies@IamPolicy:sys/publicView

# 所有用户都受特定策略控制
IamUser:*#policies@IamPolicy:sys/admin
IamUser:*#policies@IamPolicy:sys/inspector
IamUser:*#policies@IamPolicy:sys/kam

# 所有组织都受特定策略控制
IamGroup:*#policies@IamPolicy:sys/admin
IamGroup:*#policies@IamPolicy:sys/inspector
IamGroup:*#policies@IamPolicy:sys/kam
IamGroup:*#policies@IamPolicy:sys/pm
IamGroup:*#policies@IamPolicy:sys/demo

# 所有Anno资源都受管理员策略控制
AnnoProject:*#policies@IamPolicy:sys/admin
AnnoProject:*#policies@IamPolicy:sys/inspector
AnnoProject:*#policies@IamPolicy:sys/kam
AnnoProject:*#policies@IamPolicy:sys/pm
AnnoLot:*#policies@IamPolicy:sys/admin
AnnoLot:*#policies@IamPolicy:sys/inspector
AnnoOrder:*#policies@IamPolicy:sys/admin
AnnofeedData:*#policies@IamPolicy:sys/admin
AnnofeedFile:*#policies@IamPolicy:sys/admin
FePage:*#policies@IamPolicy:sys/admin
```

## 3. 动态权限规则分析 (iam/pkg/keto/)

### 3.1 权限常量定义 (perm.go)

#### 通用权限操作
```go
// 基础CRUD权限
PermCreate = "create"    // 创建
PermUpdate = "update"    // 更新
PermDelete = "delete"    // 删除
PermGet    = "get"       // 获取
PermList   = "list"      // 列表

// 扩展权限
PermStat       = "stat"       // 统计
PermGetPrivacy = "getPrivacy" // 获取隐私信息

// 策略管理权限
PermGetPolicy = "getPolicy"   // 获取策略
PermSetPolicy = "setPolicy"   // 设置策略

// 成员管理权限
PermListMember    = "listMember"    // 列出成员
PermAddMember     = "addMember"     // 添加成员
PermDeleteMember  = "deleteMember"  // 删除成员
PermSetMemberRole = "setMemberRole" // 设置成员角色
```

### 3.2 用户权限模板 (user.go)

#### 用户创建时的权限模板
```go
const userTpl = `
# 用户所有者策略
IamPolicy:IamUser.<user-name>.owner#roles@IamRole:owner
IamPolicy:IamUser.<user-name>.owner#users@IamUser:<user-name>

# 用户自我所有权关系 (快速访问路径)
IamUser:<user-name>#owners@IamUser:<user-name>
IamUser:<user-name>#policies@IamPolicy:sys/admin

# 用户加入全局用户组
IamGroup:sys/allUsers#members@IamUser:<user-name>
`
```

**作用说明**：
- 每个用户创建时自动获得对自己的所有权
- 用户自动加入全局用户组
- 用户受系统管理员策略控制

### 3.3 组织权限模板 (group.go)

#### 组织创建时的权限模板
```go
const groupTpl = `
# 组织所有者策略
IamPolicy:IamGroup.<group-name>.owner#roles@IamRole:owner
IamPolicy:IamGroup.<group-name>.owner#users@IamGroup:<group-name>.owner#members
IamGroup:<group-name>#policies@IamPolicy:IamGroup.<group-name>.owner
IamGroup:<group-name>#policies@IamPolicy:sys/admin

# 组织资源父级关系 (重要!)
AnnoLot:IamGroup.<group-name>#parents@IamGroup:<group-name>
AnnoOrder:IamGroup.<group-name>#parents@IamGroup:<group-name>
AnnofeedData:IamGroup.<group-name>#parents@IamGroup:<group-name>
AnnofeedFile:IamGroup.<group-name>#parents@IamGroup:<group-name>
`

const groupOwnerTpl = `
IamGroup:<group-name>.owner#members@IamUser:<owner-user>
IamGroup:<group-name>#members@IamUser:<owner-user>
IamUser:<owner-user>#parents@IamGroup:<group-name>
`

const orgTpl = `
# 顶级组织管理权限
IamRole:IamGroup.<group-name>#parents@IamGroup:<group-name>
IamUser:IamGroup.<group-name>#parents@IamGroup:<group-name>
IamGroup:IamGroup.<group-name>#parents@IamGroup:<group-name>
`
```

**关键理解**：
- 组织创建时会自动建立与各种资源的父级关系
- 这使得组织成员可以在该组织下创建和管理资源
- 组织所有者自动成为组织成员

### 3.4 角色权限模板 (role.go)

#### 角色创建时的权限模板
```go
const roleTpl = "IamRole:<role-name>#policies@IamPolicy:sys/admin\n"
const roleViewerTpl = "IamRole:<role-name>#policies@IamPolicy:sys/public-view\n"
const roleParentTpl = "IamRole:<role-name>#parents@IamGroup:<parent-name>\n"
```

**作用说明**：
- 所有角色都受系统管理员策略控制
- 全局角色还受公共查看策略控制
- 组织角色会建立与父组织的关系

### 3.5 资源权限模板 (object.go)

#### 资源创建时的权限模板
```go
const resourceTpl = `
# 资源所有者策略
IamPolicy:<namespace>.<resource-name>.owner#roles@IamRole:<namespace>.owner
<namespace>:<resource-name>#policies@IamPolicy:<namespace>.<resource-name>.owner
<namespace>:<resource-name>#policies@IamPolicy:sys/admin
`

const resourceOwnerUserTpl = "IamPolicy:<namespace>.<resource-name>.owner#users@IamUser:<subject-name>\n"
const resourceOwnerGroupTpl = "IamPolicy:<namespace>.<resource-name>.owner#users@IamGroup:<subject-name>#members\n"
const resourceParentTpl = "<namespace>:<resource-name>#parents@<parent>\n"
```

**重要机制**：
- 每个资源创建时都会生成专属的所有者策略
- 资源会绑定到指定的所有者(用户或组织)
- 资源会建立与父级资源的关系
- 所有资源都受系统管理员策略控制

### 3.6 策略权限模板 (policy.go)

#### 策略创建的核心逻辑
```go
func (o *AccessMgr) CreatePolicy(ctx context.Context, resource, role string, users []string) (name string, err error) {
    // 1. 解析资源名称
    rns, robj := ParseObjectName(resource)
    name = EscapeName(resource) + "." + role

    // 2. 创建策略用户关系
    tps, err := o.makePolicyUsersTuples(name, users)

    // 3. 绑定策略到角色
    tps = append(tps, NewTuple(PolicyNs, name, PolicyRelRoles, NewSubjectSet(RoleNs, role, "")))

    // 4. 绑定策略到资源
    tps = append(tps, NewTuple(rns, robj, RelPolicies, NewSubjectSet(PolicyNs, name, "")))

    return name, o.kt.Add(ctx, tps...)
}
```

**策略创建过程**：
1. 策略名称 = 资源名称 + 角色名称
2. 策略绑定指定的用户列表
3. 策略绑定指定的角色
4. 资源绑定该策略

## 4. 策略机制深度解析

### 4.1 策略的本质

**策略不是简单的用户-角色绑定**，而是一个三元关系：

```
策略 = (用户集合, 角色, 资源)
```

### 4.2 策略的工作原理

#### 步骤1：策略绑定用户
```
IamPolicy:AnnoLot:lot123.editor#users@IamUser:alice
```
定义：alice用户在AnnoLot:lot123.editor策略的用户范围内

#### 步骤2：策略绑定角色
```
IamPolicy:AnnoLot:lot123.editor#roles@IamRole:AnnoLot.editor
```
定义：AnnoLot:lot123.editor策略授予AnnoLot.editor角色

#### 步骤3：资源绑定策略
```
AnnoLot:lot123#policies@IamPolicy:AnnoLot:lot123.editor
```
定义：lot123资源受AnnoLot:lot123.editor策略控制

#### 步骤4：角色拥有权限
```
IamRole:AnnoLot.editor#<EMAIL>
```
定义：AnnoLot.editor角色拥有AnnoLot.update权限

### 4.3 权限检查的完整路径

当alice尝试更新lot123时：

```
Alice → AnnoLot:lot123.editor策略用户 → AnnoLot.editor角色 → AnnoLot.update权限 → 允许操作
```

### 4.4 为什么需要策略？

**直接绑定的问题**：
```
IamUser:alice#roles@IamRole:AnnoLot.editor  # 这样alice就拥有所有AnnoLot的编辑权限
```

**策略绑定的优势**：
```
IamPolicy:AnnoLot:lot123.editor#users@IamUser:alice     # alice只在特定策略范围内
IamPolicy:AnnoLot:lot123.editor#roles@IamRole:AnnoLot.editor  # 策略授予角色
AnnoLot:lot123#policies@IamPolicy:AnnoLot:lot123.editor      # 只对lot123生效
```

**策略实现了资源级别的权限隔离**！

## 5. 命名空间定义分析 (namespaces.keto.ts)

### 5.1 核心命名空间

#### IamPolicy - 策略命名空间
```typescript
class IamPolicy implements Namespace {
  related: {
    roles: IamRole[]  // 策略绑定的角色(只能一个)
    users: (IamUser | SubjectSet<IamGroup, "members">)[]  // 策略适用的用户
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      this.related.users.includes(ctx.subject) &&  // 用户在策略范围内
      this.related.roles.traverse((p) => p.permits.has_perm(ctx, perm)),  // 角色有权限
  }
}
```

#### IamRole - 角色命名空间
```typescript
class IamRole implements Namespace {
  related: {
    parents: IamGroup[]  // 父级组织
    policies: IamPolicy[]  // 关联的策略
    perms: (IamPerm | SubjectSet<IamRole, "perms">)[]  // 权限集合
  }

  permits = {
    has_perm: (ctx: Context, perm: string): boolean =>
      this.related.perms.includes(perm),  // 直接权限检查

    check: (ctx: Context, perm: string): boolean =>
      IamRole:"*".permits.check(ctx, perm) ||  // 全局角色权限
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||  // 父级权限
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),   // 策略权限
  }
}
```

#### IamGroup - 组织命名空间
```typescript
class IamGroup implements Namespace {
  related: {
    parents: IamGroup[]  // 父级组织
    policies: IamPolicy[]  // 关联的策略
    members: (IamUser | SubjectSet<IamGroup, "members">)[]  // 成员
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      IamGroup:"*".permits.check(ctx, perm) ||  // 全局组织权限
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||  // 父级权限
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),   // 策略权限
  }
}
```

#### IamUser - 用户命名空间
```typescript
class IamUser implements Namespace {
  related: {
    parents: IamGroup[]  // 所属组织
    policies: IamPolicy[]  // 直接关联的策略
    owners: IamUser[]  // 所有者关系
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      this == ctx.subject ||  // 用户对自己的权限
      IamUser:"*".permits.check(ctx, perm) ||  // 全局用户权限
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||  // 组织权限
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)) ||  // 策略权限
      this.related.owners.includes(ctx.subject),  // 所有者权限
  }
}
```

### 5.2 业务资源命名空间

#### AnnoLot - 标注批次
```typescript
class AnnoLot implements Namespace {
  related: {
    parents: (IamGroup|AnnoLot|AnnoProject)[]  // 支持多种父级类型
    policies: IamPolicy[]  // 关联的策略
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoLot:"*".permits.check(ctx, perm) ||  // 全局AnnoLot权限
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||  // 父级权限继承
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),   // 策略权限

    // 具体权限方法
    create: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.create"),
    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.get"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.delete"),
    listJob: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.listJob"),
    exportAnno: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.exportAnno"),
    assignExecteam: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.assignExecteam"),
  }
}
```

#### AnnoJob - 标注任务
```typescript
class AnnoJob implements Namespace {
  related: {
    parents: AnnoLot[]  // 父级批次
    policies: IamPolicy[]  // 关联的策略
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoJob:"*".permits.check(ctx, perm) ||  // 全局AnnoJob权限
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||  // 父级Lot权限
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),   // 策略权限

    // 具体权限方法
    assign: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.assign"),
    reject: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.reject"),
    log: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.log"),
  }
}
```

## 6. 权限系统的分类整理

### 6.1 按实体类型分类

#### 用户管理实体
- **IamUser**: 系统用户
- **IamGroup**: 组织/团队
- **IamRole**: 角色定义
- **IamPolicy**: 权限策略

#### 业务资源实体
- **AnnoProject**: 标注项目
- **AnnoLot**: 标注批次
- **AnnoJob**: 标注任务
- **AnnoOrder**: 标注订单
- **AnnofeedData**: 数据文件
- **AnnofeedFile**: 上传文件
- **FePage**: 前端页面

### 6.2 按权限级别分类

#### 系统级权限
- **sys/admin**: 系统管理员
- **sys/inspector**: 系统检查员
- **sys/kam**: 系统KAM
- **sys/pm**: 系统PM
- **sys/publicView**: 公共查看

#### 业务级权限
- **anno.owner**: 标注业务所有者
- **anno.kam**: 标注业务KAM
- **anno.pm**: 标注业务PM
- **anno.regulator**: 标注监管者
- **anno.viewer**: 标注查看者

#### 资源级权限
- **AnnoLot.owner**: 批次所有者
- **AnnoLot.editor**: 批次编辑者
- **AnnoLot.viewer**: 批次查看者
- **AnnoJob.owner**: 任务所有者
- **AnnoJob.regulator**: 任务监管者

### 6.3 按操作类型分类

#### 基础CRUD操作
- **create**: 创建资源
- **get**: 获取资源
- **update**: 更新资源
- **delete**: 删除资源
- **list**: 列出资源

#### 业务特定操作
- **listJob**: 列出任务
- **exportAnno**: 导出标注
- **assignExecteam**: 分配执行团队
- **assign**: 分配任务
- **reject**: 拒绝任务
- **log**: 查看日志

#### 管理操作
- **getPolicy**: 获取策略
- **setPolicy**: 设置策略
- **addMember**: 添加成员
- **deleteMember**: 删除成员
- **setMemberRole**: 设置成员角色

## 7. 实际业务场景分析

### 7.1 用户A获得基础权限的完整流程

#### 场景：用户Alice加入组织org123并获得AnnoLot查看权限

**步骤1：用户创建**
```go
// iam/pkg/keto/user.go - CreateUsers
err := keto.DefaultAccessMgr().CreateUsers(ctx, "alice")
```
生成的权限规则：
```
IamPolicy:IamUser.alice.owner#roles@IamRole:owner
IamPolicy:IamUser.alice.owner#users@IamUser:alice
IamUser:alice#owners@IamUser:alice
IamUser:alice#policies@IamPolicy:sys/admin
IamGroup:sys/allUsers#members@IamUser:alice
```

**步骤2：用户加入组织**
```go
// iam/pkg/keto/group.go - AddGroupMembers
err := keto.DefaultAccessMgr().AddGroupMembers(ctx, "org123", "member", true, []string{"alice"})
```
生成的权限规则：
```
IamGroup:org123#members@IamUser:alice
```

**步骤3：组织绑定查看策略**
```go
// iam/pkg/keto/policy.go - CreatePolicy
name, err := keto.DefaultAccessMgr().CreatePolicy(ctx, "IamGroup:org123", "anno.viewer", []string{"IamGroup:org123#members"})
```
生成的权限规则：
```
IamPolicy:IamGroup.org123.anno.viewer#roles@IamRole:anno.viewer
IamPolicy:IamGroup.org123.anno.viewer#users@IamGroup:org123#members
IamGroup:org123#policies@IamPolicy:IamGroup.org123.anno.viewer
```

**步骤4：权限检查**
当Alice尝试查看AnnoLot时：
```go
// apis/client/iam.go - IsAllowed
allowed := IsAllowed(ctx, "alice", "list", "AnnoLot", "org123")
```

**Keto权限计算路径**：
```
1. 检查: AnnoLot:org123#list@IamUser:alice
2. 查找AnnoLot:org123的策略: IamPolicy:IamGroup.org123.anno.viewer
3. 检查alice是否在策略用户范围: IamGroup:org123#members@IamUser:alice ✓
4. 检查策略角色是否有权限: IamRole:anno.viewer#perms@IamRole:AnnoLot.viewer#perms ✓
5. 检查AnnoLot.viewer是否有list权限: IamRole:AnnoLot.viewer#<EMAIL> ✓
6. 返回: 允许
```

### 7.2 创建Lot时的权限创建流程

#### 场景：Alice在org123中创建一个新的AnnoLot

**步骤1：权限检查**
```go
// anno/internal/service/lot.go - CreateLot
if !client.IsAllowed(ctx, "", biz.PermCreate, biz.PermClsLot, client.GroupScope(req.OrgUid)) {
    return nil, errors.NewErrForbidden()
}
```

**步骤2：创建业务资源**
```go
// anno/internal/biz/lot.go - Create
lot, err = o.repo.Create(ctx, p)
```

**步骤3：创建权限策略**
```go
// anno/internal/biz/lot.go - Create
err = client.CreateAccessPolicies(ctx, PermClsLot, p.GetUid(),
    []string{client.UserScope(p.CreatorUid)},     // 创建者作为owner
    []string{client.GroupScope(p.OrgUid)})        // 所属组织作为parent
```

**生成的权限规则**：
```
# 资源所有者策略
IamPolicy:AnnoLot.lot123.owner#roles@IamRole:AnnoLot.owner
IamPolicy:AnnoLot.lot123.owner#users@IamUser:alice
AnnoLot:lot123#policies@IamPolicy:AnnoLot.lot123.owner
AnnoLot:lot123#policies@IamPolicy:sys/admin

# 资源父级关系
AnnoLot:lot123#parents@IamGroup:org123
```

## 8. 策略机制的深度理解

### 8.1 策略 vs 直接角色绑定

#### ❌ 错误理解：直接绑定
```
IamUser:alice#roles@IamRole:AnnoLot.editor
```
**问题**：Alice会拥有所有AnnoLot的编辑权限，无法实现资源隔离

#### ✅ 正确理解：策略绑定
```
IamPolicy:AnnoLot.lot123.editor#users@IamUser:alice      # Alice在策略范围内
IamPolicy:AnnoLot.lot123.editor#roles@IamRole:AnnoLot.editor  # 策略授予角色
AnnoLot:lot123#policies@IamPolicy:AnnoLot.lot123.editor       # 资源绑定策略
```
**优势**：Alice只对lot123有编辑权限，实现了资源级隔离

### 8.2 策略的三重绑定机制

#### 绑定1：策略 ↔ 用户
```
IamPolicy:策略名#users@用户或用户组
```
**作用**：定义策略适用的用户范围

#### 绑定2：策略 ↔ 角色
```
IamPolicy:策略名#roles@角色
```
**作用**：定义策略授予的角色权限

#### 绑定3：资源 ↔ 策略
```
资源:资源名#policies@策略
```
**作用**：定义资源受哪些策略控制

### 8.3 策略检查的逻辑

当检查用户是否有权限时，Keto会验证：

1. **用户匹配**：用户是否在策略的用户范围内？
2. **角色权限**：策略绑定的角色是否有所需权限？
3. **资源绑定**：资源是否受该策略控制？

**只有三个条件都满足，权限检查才会通过！**

### 8.4 策略的命名规范

#### 系统策略
```
sys/admin      # 系统管理员策略
sys/inspector  # 系统检查员策略
sys/kam        # 系统KAM策略
sys/pm         # 系统PM策略
```

#### 资源策略
```
资源类型:资源ID.角色名
AnnoLot:lot123.owner    # lot123的所有者策略
AnnoJob:job456.editor   # job456的编辑者策略
IamGroup:org123.member  # org123的成员策略
```

## 9. 权限系统总结

### 9.1 系统优势

#### 细粒度控制
- 支持资源级别的权限控制
- 支持操作级别的权限控制
- 支持用户、角色、组织的多维度权限管理

#### 灵活的权限继承
- 用户可以通过组织获得权限
- 角色可以继承其他角色的权限
- 资源可以继承父级资源的权限
- 全局策略可以覆盖所有资源

#### 动态权限管理
- 运行时创建和删除权限规则
- 支持权限的动态分配和回收
- 支持复杂的业务权限逻辑

### 9.2 关键设计原则

#### 最小权限原则
- 用户默认没有任何权限
- 必须通过策略显式授权
- 权限检查采用白名单模式

#### 权限隔离原则
- 不同组织的资源相互隔离
- 不同项目的资源相互隔离
- 通过策略实现资源级权限控制

#### 可扩展性原则
- 新增资源类型只需定义新的命名空间
- 新增权限操作只需添加权限常量
- 新增业务角色只需配置权限规则

### 9.3 适用场景

#### 多租户系统
- 支持多个组织的权限隔离
- 支持组织内部的权限管理
- 支持跨组织的权限协作

#### 复杂业务流程
- 支持工作流的权限控制
- 支持审批流程的权限管理
- 支持任务分配的权限控制

#### 细粒度访问控制
- 支持API级别的权限控制
- 支持数据级别的权限控制
- 支持功能级别的权限控制

这个权限系统通过策略机制实现了用户、角色、资源之间的灵活绑定，既保证了权限的细粒度控制，又实现了良好的可扩展性和可维护性。策略是整个系统的核心，它不仅仅是用户和角色的纽带，更是实现资源级权限隔离的关键机制。
