package keto

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"strings"

	keto "github.com/ory/keto/proto/ory/keto/relation_tuples/v1alpha2"
)

// user policy template
const (
	UserNs = "IamUser"

	userTpl = `
# user owners policy
IamPolicy:IamUser.<user-name>.owner#roles@IamRole:owner
IamPolicy:IamUser.<user-name>.owner#users@IamUser:<user-name>

# the owners relation makes a fast access (short path)
IamUser:<user-name>#owners@IamUser:<user-name>
IamUser:<user-name>#policies@IamPolicy:sys/admin
# the owners relation makes this policy useless
#IamUser:<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
IamGroup:sys/allUsers#members@IamUser:<user-name>

# make users can list resources created by his own
# should attach IamPolicy:sys/admin?
AnnoLot:IamUser.<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
AnnoOrder:IamUser.<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
AnnofeedData:IamUser.<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
AnnofeedFile:IamUser.<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
`
	// TODO: 当新加资源类型时，如何使已有用户和新增用户在自己的空间里查询和创建？
	//       Option 1. 每当用户新建资源时，同时检查是否存在规则 xxx:IamUser.<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
	//                 如不存在，则增加；
	//                 但如何解决在新建资源之前，查询资源列表的权限问题？
)

func init() {
	// validate templates
	for _, s := range []string{userTpl} {
		_, err := ParseTuples(s)
		if err != nil {
			panic(fmt.Errorf("failed to parse user policy template \n: %v\n: %w", s, err))
		}
	}
}

func MakeUserName(uid string) string { return MakeObjectName(UserNs, uid) }

func (o *AccessMgr) CreateUsers(ctx context.Context, uids ...string) (err error) {
	fmt.Println("---> Creating Users: ", uids)
	tps := make([]*RelationTuple, 0, len(uids)*4)
	for _, uid := range uids {
		s := strings.ReplaceAll(userTpl, "<user-name>", uid)
		t, err := ParseTuples(s)
		if err != nil {
			return fmt.Errorf("failed to parse user policy template: %w", err)
		}
		tps = append(tps, t...)
	}
	spew.Dump("---> create users tuples: ", tps)
	return o.kt.Add(ctx, tps...)
}

type AccessRequest struct {
	Subject string
	Perm    string
	Object  string
}

// Check checks each access request one by one. It only returns true if all the requests are allowed.
func (o *AccessMgr) Check(ctx context.Context, in []*AccessRequest) (allowed bool, err error) {
	for _, e := range in {
		if allowed, err = o.AllowE(ctx, e.Subject, e.Perm, e.Object); !allowed {
			return
		}
	}
	return true, nil
}

func (o *AccessMgr) Allow(ctx context.Context, user, perm, obj string) (allowed bool) {
	allowed, err := o.AllowE(ctx, user, perm, obj)
	fmt.Println("---> iam force allow: ", allowed)
	fmt.Println()
	if !allowed {
		o.kt.log.Warn(ctx, "access is denied", "user", user, "perm", perm, "obj", obj, "error", err)
	}
	//allowed = true
	//err = nil
	return
}

func (o *AccessMgr) AllowE(ctx context.Context, user, perm, obj string) (allowed bool, err error) {
	fmt.Println("---> check AllowE !")
	fmt.Println("---> user: ", user, ", perm: ", perm, ", obj: ", obj)
	t, err := o.makeCheckTuple(user, perm, obj)
	spew.Dump("---> check tuple: ", t)
	if err != nil {
		return
	}
	return o.kt.Check(ctx, &CheckRequest{Tuple: t})
}

func (o *AccessMgr) makeCheckTuple(user, perm, obj string) (*RelationTuple, error) {
	uns, uname, err := ParseUserGroupName(user)
	if err != nil {
		return nil, err
	}
	ons, oname := ParseObjectName(obj)
	if ons == "" {
		return nil, fmt.Errorf("bad object: %v", obj)
	}
	oname = EscapeName(oname)
	if oname == "" {
		oname = GlobalScope
	}
	return NewTuple(ons, oname, perm, NewSubjectSet(uns, uname, "")), nil
}

func (o *AccessMgr) DeleteUser(ctx context.Context, uid string) (err error) {
	// delete tuples: *:IamUser.<user-name>#policies@*
	rsp, err := o.kt.List(ctx, &keto.ListRelationTuplesRequest{
		RelationQuery: NewQuery("", MakeScopeObj(UserNs, uid), RelPolicies, nil),
	})
	if err != nil {
		o.kt.log.Error(ctx, "failed to query tuples", err, "tuple", fmt.Sprintf("*:%v#%v@*", MakeScopeObj(UserNs, uid), RelPolicies))
	} else if tps := rsp.RelationTuples; len(tps) > 0 {
		// FIXME: what if the tuples are more than 100?
		err = o.kt.Delete(ctx, tps...)
		if err != nil {
			o.kt.log.Error(ctx, "failed to delete tuples", err, "count", len(tps), "tuple", fmt.Sprintf("*:%v#%v@*", MakeScopeObj(UserNs, uid), RelPolicies))
		}
	}

	return o.DeleteObject(ctx, MakeObjectName(UserNs, uid))
}
